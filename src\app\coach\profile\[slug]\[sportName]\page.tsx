import ClientGuard from "@/components/ClientGuard"
import CoachSportProfile from "@/components/coach/coachSportProfile/CoachSportProfile"

interface CoachProfilePageProps {
    params: { sportName: string }
}

const CoachSportProfilePage = ({ params }: CoachProfilePageProps) => {
    const { sportName } = params

    return (
        <ClientGuard allowedRoles={[3]}>
            <CoachSportProfile params={params} />
        </ClientGuard>
    )
}

export default CoachSportProfilePage
