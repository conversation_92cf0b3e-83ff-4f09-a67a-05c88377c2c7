export const Prevent_Charaters = /[^0-9.]/g;
export const preventNumbers = /\d/g;
export const preventSpecialChar = /[^\w\s]/gi;
export const preventSpaces = / +(?= )/g;
export const EmailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
export const PasswordRegex =
  /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$/;
export const onlyNumbers = /\D/g;
export const onlyAlphabets = /[a-zA-Z]/g;
export const urlRegex = /^(https?:\/\/)?(www\.)?[a-zA-Z0-9\-]+\.[a-zA-Z]{2,}(\/\S*)?$/;

export const isValidUrl = (url: string) => urlRegex.test(url);

export const isValidPassword = (password: string) => {
  if (password.length < 1) return "New password is required";
  if (password.length < 8)
    return "New password must be at least 8 characters long";
  if (!/[A-Z]/.test(password))
    return "New password must contain at least one uppercase letter";
  if (!/[a-z]/.test(password))
    return "New password must contain at least one lowercase letter";
  if (!/[0-9]/.test(password))
    return "New password must contain at least one number";
  if (!/[#?!@$%^&*\-_]/.test(password))
    return "New password must contain at least one special character";

  return true;
};
