import { useHandleAthleteSectionExpose } from "@/hooks/useAthleteExposeSections"
import { useTokenValues } from "@/hooks/useTokenValues"
import { AppDispatch, RootState } from "@/store"
import { deleteAthleteSportVictoryVault, fetchAthleteSportMilestones, fetchAthleteSportVictoryVault, handleUpdateUserInput, postAthleteSportVictoryVault, putAthleteSportVictoryVault } from "@/store/slices/athlete/athleteSportProfileSlice"
import { EachMileStoneVictoryItem } from "@/utils/interfaces"
import { format } from "date-fns"
import { Params } from "next/dist/shared/lib/router/utils/route-matcher"
import { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"
import AchievementItem from "../common/AchievementItem"
import AddMileStoneVicoryVault from "../common/AddMileStoneVicoryVault"
import { Switch } from "../ui/switch"
import { useLocalStoredInfo } from "@/hooks/useLocalStoredInfo"

interface IProps {
    params: Params
}
const VictoryVault = ({ params }: IProps) => {
    const { vaultId, toggleVictoryVault, isVictoryVault, vicotryVaultData, addedVictoryVaultList, apiStatus } = useSelector((state: RootState) => state.athleteSportProfile)
    const dispatch = useDispatch<AppDispatch>()
    const { profileId, } = useLocalStoredInfo()
    const { userId, roleId } = useTokenValues()
    const decodedSportName = decodeURIComponent(params?.sportName);
    const { handleAthleteSportsSectionsHide } = useHandleAthleteSectionExpose()

    useEffect(() => {
        dispatch(fetchAthleteSportVictoryVault(params?.sportId))
    }, [dispatch])

    const handleToggleSection = (checked) => {
        handleAthleteSportsSectionsHide(params?.sportId, 'toggleVictoryVault', checked)
    }

    const handleAddModal = () => {
        dispatch(handleUpdateUserInput({ name: 'vaultId', value: null }))
        dispatch(handleUpdateUserInput({ name: 'isVictoryVault', value: !isVictoryVault }))
        dispatch(handleUpdateUserInput({ name: 'vicotryVaultData', value: null }))
    }

    const handleSaveVictoryVault = async (data: Partial<EachMileStoneVictoryItem> | null) => {
        const payload = {
            roleId,
            athleteId: profileId,
            userId,
            sportId: Number(params?.sportId),
            achievementTitle: data?.title,
            achievementBlurb: data?.blurb,
            achievementDesc: null,
            achievementUrl: data?.link,
            achievementDate: data?.date ? format(new Date(data?.date), 'yyyy-MM-dd') : null,
            genTagId1: data?.tags?.length! > 0 ? data?.tags?.[0]?.value : null,
            genTagId2: data?.tags?.length! > 1 ? data?.tags?.[1]?.value : null,
            genTagId3: data?.tags?.length! > 2 ? data?.tags?.[2]?.value : null,
            genTagId4: null,
            genTagId5: null,
            genTagId6: null,
            mediaType: null,
            mediaUrl: data?.file,
            isHidden: false
        }

        try {
            if (vaultId) {
                const resultAction = await dispatch(putAthleteSportVictoryVault({ payload, vaultId }))
                if (putAthleteSportVictoryVault.fulfilled.match(resultAction)) {
                    await dispatch(fetchAthleteSportVictoryVault(params?.sportId))
                    handleAddModal()
                }
            } else {
                const resultAction = await dispatch(postAthleteSportVictoryVault(payload))
                if (postAthleteSportVictoryVault.fulfilled.match(resultAction)) {
                    await dispatch(fetchAthleteSportVictoryVault(params?.sportId))
                    dispatch(handleUpdateUserInput({ name: 'isVictoryVault', value: false }))
                }
            }
        } catch (error) {
            console.log(error)
        }
    }

    const handleEditAchievement = (id: number) => {
        dispatch(handleUpdateUserInput({ name: 'vaultId', value: id }))
        dispatch(handleUpdateUserInput({ name: 'isVictoryVault', value: true }))
        const pickedData = addedVictoryVaultList?.find(each => each?.id === id)
        dispatch(handleUpdateUserInput({ name: 'vicotryVaultData', value: { ...pickedData, date: pickedData?.date && new Date(pickedData?.date) } }))
    }

    const handleDeleteAchievement = async (id) => {
        try {
            const resultAction = await dispatch(deleteAthleteSportVictoryVault(id))
            if (deleteAthleteSportVictoryVault.fulfilled.match(resultAction)) {
                await dispatch(fetchAthleteSportMilestones(params?.sportId))
            }
        } catch (error) {
            console.log(error)
        }
    }

    return (
        <>
            <div className="bg-slate-100 rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-center gap-5">
                    <h3 className="text-xl font-bold">{decodedSportName ?? `${decodedSportName} - `} Victory Vault</h3>
                    <Switch checked={toggleVictoryVault} onCheckedChange={handleToggleSection} />
                </div>
                {toggleVictoryVault ?
                    <>
                        <div className="flex items-end justify-end">
                            <AddMileStoneVicoryVault
                                origin='Victory Vault'
                                open={isVictoryVault}
                                handleAddModal={handleAddModal}
                                sourceData={vicotryVaultData}
                                handleSaveForm={handleSaveVictoryVault}
                                loading={apiStatus === 'victoryPending'}
                                isEdit={Boolean(vaultId)}
                            />
                        </div>
                        {addedVictoryVaultList?.length > 0 ? addedVictoryVaultList?.map(each =>
                            <AchievementItem
                                item={each}
                                key={each?.id + 'vv'}
                                handleEdit={handleEditAchievement}
                                handleDelete={handleDeleteAchievement}
                            />
                        ) :
                            <div className="flex items-center justify-center py-5">
                                <p className="text-center text-gray-600 text-sm w-3/4">
                                    Start building your legacy — add your {decodedSportName}  victory vault!
                                </p>
                            </div>
                        }
                    </> : null}
            </div>
        </>
    )
}
export default VictoryVault