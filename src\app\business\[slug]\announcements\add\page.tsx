
"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { AppDispatch, RootState } from "@/store";
import { fetchAllStates, fetchAllLocations } from "@/store/slices/commonSlice";
import {
  fetchAnnouncementsCategory,
  fetchAnnouncementType,
  createAnnouncement,
  imageAnnouncement
} from "@/store/slices/org-announcement/announcement";
import { useSnackbar } from "@/contexts/SnackbarContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Upload, Calendar, MapPin, Phone, Mail, Link, ArrowLeft } from "lucide-react";
import { BusinessRoutes } from "@/utils/businessRoutes";
import DateRangePicker from "@/components/common/DatePicker";
import { channelsList } from "@/utils/constantDropdown";
import { useForm, useFieldArray, Controller } from 'react-hook-form';


interface VideoLink {
  label: string;
  videoUrl: string;
}

export interface AnnouncementFormData {
  title: string;
  fullContent: string;
  image: string | null;
  dateRange: {
    start: Date | null;
    end: Date | null;
  } | null;
  area: string | null;
  type: string;
  category: string;
  announcementFormat: string;
  registrationLink: string | null;
  address: string | null;
  state: string | null;
  city: string | null;
  zipcode: string | null;
  firstName: string | null;
  lastName: string | null;
  contactPhone: string | null;
  contactEmail: string | null;
  // quickLinks: string[] | null;
  videos: VideoLink[] | null;
  isPublished: boolean;
}

// Validation Schema
const validationSchema = yup.object().shape({
  title: yup
    .string()
    .required("Title is required")
    .min(3, "Title must be at least 3 characters")
    .max(100, "Title must not exceed 100 characters"),

  fullContent: yup
    .string()
    .required("Announcement Details is required")
    .min(10, "Announcement Details must be at least 10 characters")
    .max(500, "Announcement Details must not exceed 500 characters"),

  image: yup.string().nullable(),

  dateRange: yup
    .object()
    .shape({
      start: yup.date().required("Start date is required"),
      end: yup.date()
        .required("End date is required")
        .when('start', {
          is: (start: Date) => start !== null,
          then: (schema) => schema.min(
            yup.ref('start'),
            "End date must be after start date"
          ),
        }),
    })
    .required("Date range is required"),

  area: yup.string().nullable(),

  type: yup
    .string()
    .required("Type is required"),

  category: yup
    .string()
    .required("Category is required"),

  announcementFormat: yup
    .string()
    .required("Announcement format is required"),

  registrationLink: yup
    .string()
    .required("Please enter a valid URL")
    .nullable()
    .test('url', 'Please enter a valid URL', function (value) {
      if (!value || value.trim() === '') return true;
      return /^https?:\/\/.+/.test(value);
    }),

  address: yup
    .string()
    .required("Location address is required")
    .min(10, "Location address must be at least 10 characters")
    .max(500, "Location address must not exceed 500 characters")
    .matches(
      /^[a-zA-Z0-9\s,.#-]+$/,
      "Location address can only contain letters, numbers, spaces, commas, periods, hyphens, and #"
    ),
  state: yup
    .string()
    .required("State is required")
    .test("valid-state", "Please select a valid state", (value) => {
      return value !== "" && value !== undefined;
    }),
  city: yup
    .string()
    .required("City is required")
    .test("valid-city", "Please select a valid city", (value) => {
      return value !== "" && value !== undefined;
    }),
  zipcode: yup
    .string()
    .required("Zipcode is required")
    .matches(/^\d{5}(-\d{4})?$/, "Please enter a valid zipcode (e.g., 12345 or 12345-6789)")
    .test("zipcode-length", "Zipcode must be 5 or 9 digits", (value) => {
      if (!value) return false;
      const cleaned = value.replace("-", "");
      return cleaned.length === 5 || cleaned.length === 9;
    }),

  firstName: yup
    .string()
    .required("first Name is required")
    .min(3, "first Name must be at least 3 characters")
    .max(10, "first Name must not exceed 10 characters"),

  lastName: yup
    .string()
    .required("last Name is required")
    .min(3, "last Name must be at least 3 characters")
    .max(10, "last Name must not exceed 10 characters"),

  contactPhone: yup
    .string()
    .required("Phone Number is required")
    .matches(
      /^(\d\s?){10}$/,
      "Phone Number must be 10 digitnumber"
    ),

  contactEmail: yup
    .string()
    .email("Invalid Email")
    .required("Email Address Is Required"),

  // quickLinks: yup.array().of(yup.string()).nullable(),

  videos: yup.array()
    .of(
      yup.object().shape({
        label: yup.string().required('Video label is required'),
        videoUrl: yup.string().url('Must be a valid URL').required('Video URL is required'),
      })
    )
    .min(1, 'At least one video is required')
    .max(5, 'Up to 5 videos only')
    .required('Videos are required'),

  isPublished: yup.boolean().required()
});

const AddAnnouncementPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const dispatch = useDispatch<AppDispatch>();
  const { showSuccess, showError } = useSnackbar();

  // Redux state
  const {
    annoucementType,
    orgAnnouncement: announcementCategories,
    loading,
  } = useSelector((state: RootState) => state.announcementDashboard);


  const { orgProfile } = useSelector((state: RootState) => state.orgProfile);


  // Common slice state for states and locations
  const {
    allStatesList,
    allLocationsList,
  } = useSelector((state: RootState) => state.commonSlice);

  const { imageData } = useSelector((state: RootState) => state.announcementDashboard);

  // React Hook Form setup
  const {
    control,
    handleSubmit: onSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
    reset,
    setError,
    clearErrors
  } = useForm<AnnouncementFormData>({
    resolver: yupResolver(validationSchema) as any,
    mode: 'onChange',
    defaultValues: {
      title: "",
      fullContent: "",
      image: null,
      dateRange: null,
      area: null,
      type: "",
      category: "",
      announcementFormat: "",
      registrationLink: "",
      address: null,
      state: null,
      city: null,
      zipcode: null,
      firstName: null,
      lastName: null,
      contactPhone: null,
      contactEmail: null,
      // quickLinks: null,
      videos: [{ label: '', videoUrl: '' }],
      isPublished: false,
    }
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'videos',
  });

  // Watch form values for dependent fields
  const watchedState = watch("state");
  const watchedFullContent = watch("fullContent");
  const watchedDateRange = watch("dateRange");
  const watchedType = watch("type");
  const watchedCategory = watch("category");
  const watchedAnnouncementFormat = watch("announcementFormat");
  const watchedRegistrationLink = watch("registrationLink");
  const watchedAddress = watch("address");
  const watchedCity = watch("city");
  const watchedZipcode = watch("zipcode");
  const watchedFirstName = watch("firstName");
  const watchedLastName = watch("lastName");
  const watchedContactPhone = watch("contactPhone");
  const watchedContactEmail = watch("contactEmail");
  const watchedVideos = watch("videos");

  const [imagePreview, setImagePreview] = useState<string | null>(null);

  // Load dropdown data
  useEffect(() => {
    dispatch(fetchAnnouncementsCategory());
    dispatch(fetchAnnouncementType());
    dispatch(fetchAllStates());
  }, [dispatch]);

  // Fetch cities when state is selected
  useEffect(() => {
    if (watchedState) {
      const selectedStateId = parseInt(watchedState);
      if (selectedStateId) {
        dispatch(fetchAllLocations(selectedStateId));
      }
    }
  }, [watchedState, dispatch]);

  // Parse business info from slug
  const businessInfo = BusinessRoutes.parseSlug(params.slug as string);

  const handleInputChange = (field: keyof AnnouncementFormData, value: any) => {
    setValue(field, value);
    // Clear error for this field when user starts typing
    if (errors[field]) {
      clearErrors(field);
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/png', 'image/jpg', 'image/jpeg'];
      if (!allowedTypes.includes(file.type)) {
        showError("Please select a valid image file (PNG, JPG, JPEG).", "Invalid File Type");
        return;
      }

      // Validate file size (e.g., max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB in bytes
      if (file.size > maxSize) {
        showError("Image size should be less than 5MB.", "File Too Large");
        return;
      }

      // Convert to base64
      const reader = new FileReader();
      reader.onload = (e) => {
        const base64String = e.target?.result as string;
        const clean = base64String.replace(/^data:[^;]+;base64,/, "");
        const byteChars = atob(clean);
        const byteNumbers = new Array(byteChars.length).fill(0).map((_, i) => byteChars.charCodeAt(i));
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: file.type });

        const formData = new FormData();
        formData.append("image", blob, file.name);
        if (base64String) {
          dispatch(imageAnnouncement(formData));
        }


        setValue('image', base64String);

        // Set preview
        setImagePreview(base64String);
      };
      reader.onerror = () => {
        showError("Error reading the image file.", "Upload Error");
      };
      reader.readAsDataURL(file);
    }
  };

  // Video management functions
  const handleFormSubmit = async (data: AnnouncementFormData) => {
    try {
      const validVideos = (data.videos ?? [])
        .filter(v =>
          v.label.trim() &&
          v.videoUrl.trim() &&
          /^https?:\/\//.test(v.videoUrl.trim())
        );
      const dummyData = {
        organizationId: orgProfile[0]?.id || 16,
        orgUserId: orgProfile[0]?.userId || 444,
        "announceTypeId": data.type ? parseInt(data.type) : null,
        "announceCategoryId": data.category ? parseInt(data.category) : null,
        "title": data.title,
        "announcementFormat": data.announcementFormat,
        "fullContent": data.fullContent,
        "startDate": data.dateRange?.start?.toISOString().split("T")[0] ?? null,
        "endDate": data.dateRange?.end?.toISOString().split("T")[0] ?? null,
        "isPinned": data.isPublished,
        location: {
          "address": data.address,
          "stateId": data.state ? parseInt(data.state) : null,
          "cityId": data.city ? parseInt(data.city) : null,
          "zipCode": data.zipcode,
        },
        contact: {
          "firstName": data.firstName,
          "lastName": data.lastName,
          "email": data.contactEmail,
          "phone": data.contactPhone
        },
        registrationLink: data.registrationLink,
        image: data.image ? imageData.imageUrl : null,
        video: validVideos
      }


      // Call API
      const result = await dispatch(createAnnouncement(dummyData));

      if (createAnnouncement.fulfilled.match(result)) {
        showSuccess("Announcement created successfully!", "Success");

        // Navigate back to announcements list
        const announcementsUrl = BusinessRoutes.announcements(businessInfo.businessName, businessInfo.userId);
        router.push(announcementsUrl);
      } else {
        showError("Failed to create announcement. Please try again.", "Error");
      }

    } catch (error) {
      console.error("Error creating announcement:", error);
      showError("Failed to create announcement. Please try again.", "Error");
    }
  };

  const handleCancel = () => {
    const announcementsUrl = BusinessRoutes.announcements(businessInfo.businessName, businessInfo.userId);
    router.push(announcementsUrl);
  };

  const handleBackToProfile = () => {
    const profileUrl = BusinessRoutes.profile(businessInfo.businessName, businessInfo.userId);
    router.push(profileUrl);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={handleBackToProfile}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Profile
              </Button>
              <div className="h-6 w-px bg-gray-300" />
              <h1 className="text-xl font-semibold text-gray-900">Add New Announcement</h1>
            </div>
            <Controller
              name="isPublished"
              control={control}
              render={({ field }) => (
                <div className="flex items-center gap-2">
                  <Label htmlFor="publish-toggle" className="text-sm font-medium">
                    Publish
                  </Label>
                  <Switch
                    id="publish-toggle"
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </div>
              )}
            />
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <form onSubmit={onSubmit(handleFormSubmit as any)} className="space-y-8">
          {/* Title and Image Section */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Title and Details */}
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Announcement Title</CardTitle>
                </CardHeader>
                <CardContent>
                  <Controller
                    name="title"
                    control={control}
                    render={({ field }) => (
                      <div>
                        <Input
                          {...field}
                          placeholder="Enter announcement title"
                          className="text-lg"
                        />
                        {errors.title && (
                          <p className="text-red-500 text-sm mt-1">{errors.title.message}</p>
                        )}
                      </div>
                    )}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Announcement Details</CardTitle>
                  <p className="text-sm text-gray-600">(max 500 chars)</p>
                </CardHeader>
                <CardContent>
                  <Controller
                    name="fullContent"
                    control={control}
                    render={({ field }) => (
                      <div>
                        <Textarea
                          {...field}
                          placeholder="Enter announcement details..."
                          maxLength={500}
                          rows={6}
                          className="resize-none"
                        />
                        <div className="text-right text-sm text-gray-500 mt-2">
                          {watchedFullContent?.length || 0}/500
                        </div>
                        {errors.fullContent && (
                          <p className="text-red-500 text-sm mt-1">{errors.fullContent.message}</p>
                        )}
                      </div>
                    )}
                  />
                </CardContent>
              </Card>
            </div>

            {/* Image Upload */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle>Upload Image</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                    {imagePreview ? (
                      <div className="space-y-4">
                        <img
                          src={imagePreview}
                          alt="Preview"
                          className="w-full h-32 object-cover rounded-lg"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => {
                            setImagePreview(null);
                            setValue('image', null);
                            // Reset the file input
                            const fileInput = document.getElementById('image-upload') as HTMLInputElement;
                            if (fileInput) fileInput.value = '';
                          }}
                        >
                          Remove Image
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                        <div>
                          <Label htmlFor="image-upload" className="cursor-pointer">
                            <span className="text-blue-600 hover:text-blue-500">Upload Image</span>
                            <span className="text-gray-500"> - png, jpg, jpeg</span>
                          </Label>
                          <Input
                            id="image-upload"
                            type="file"
                            accept="image/png,image/jpg,image/jpeg"
                            onChange={handleImageUpload}
                            className="hidden"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                  {errors.image && (
                    <p className="text-red-500 text-sm mt-1">{errors.image.message}</p>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Date Selection Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Date Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Controller
                    name="dateRange"
                    control={control}
                    render={({ field, fieldState: { error } }) => (
                      <div className="space-y-2">
                        <Label>Date Range *</Label>
                        <DateRangePicker
                          startDate={field.value?.start ?? null}
                          endDate={field.value?.end ?? null}
                          onDateChange={(dates) => {
                            const [start, end] = dates;
                            field.onChange({ start, end });
                          }}
                        />
                        {error && (
                          <p className="text-red-500 text-sm mt-1">
                            {error.message || "Please select both start and end dates"}
                          </p>
                        )}
                      </div>
                    )}
                  />

                </div>

              </div>
            </CardContent>
          </Card>

          {/* Category and Type Section */}
          <Card>
            <CardHeader>
              <CardTitle>Classification</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="type">Type</Label>
                  <Select value={watchedType} onValueChange={(value) => handleInputChange('type', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Type" />
                    </SelectTrigger>
                    <SelectContent>
                      {annoucementType?.data?.map((type: any) => (
                        <SelectItem key={type.id} value={type.id.toString()}>
                          {type.typeName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.type && (
                    <p className="text-red-500 text-sm mt-1">{errors.type.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Select value={watchedCategory} onValueChange={(value) => handleInputChange('category', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Category" />
                    </SelectTrigger>
                    <SelectContent>
                      {announcementCategories?.data?.map((category: any) => (
                        <SelectItem key={category.id} value={category.id.toString()}>
                          {category.categoryName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.category && (
                    <p className="text-red-500 text-sm mt-1">{errors.category.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="announcementFormat">Announcement Format</Label>
                  <Select value={watchedAnnouncementFormat} onValueChange={(value) => handleInputChange('announcementFormat', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select Announcement Format" />
                    </SelectTrigger>
                    <SelectContent>
                      {channelsList?.map((channel: any) => (
                        <SelectItem key={channel.value} value={channel.value.toString()}>
                          {channel.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.announcementFormat && (
                    <p className="text-red-500 text-sm mt-1">{errors.announcementFormat.message}</p>
                  )}
                </div>

                <Controller
                  name="registrationLink"
                  control={control}
                  render={({ field }) => (
                    <div>
                      <label className="block text-sm font-medium">Registration Link</label>
                      <Input
                        id="registrationLink"
                        placeholder="Enter Registration Link"
                        value={watchedRegistrationLink || ""}
                        onChange={(e) => handleInputChange('registrationLink', e.target.value)}
                      />
                      {errors.registrationLink && (
                        <p className="text-red-500 text-sm mt-1">{errors.registrationLink.message}</p>
                      )}
                    </div>
                  )}
                />

              </div>
            </CardContent>
          </Card>

          {/* Location Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="w-5 h-5" />
                Location Info (if applicable)
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <Label htmlFor="location">Location Address *</Label>
                  <Controller
                    name="address"
                    control={control}
                    rules={{
                      required: "Location address is required",
                      minLength: { value: 10, message: "Location address must be at least 10 characters" },
                      maxLength: { value: 500, message: "Location address must not exceed 500 characters" },
                      pattern: {
                        value: /^[a-zA-Z0-9\s,.#-]+$/,
                        message: "Location address can only contain letters, numbers, spaces, commas, periods, hyphens, and #"
                      }
                    }}
                    render={({ field }) => (
                      <div>
                        <Textarea
                          {...field}
                          id="location"
                          placeholder="Enter full address or location details"
                          rows={3}
                          className="mt-2"
                          value={watchedAddress || ""}
                        />
                        {errors.address && (
                          <p className="text-red-500 text-sm mt-1">
                            {errors.address.message}
                          </p>
                        )}
                      </div>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* State */}
                  <div className="space-y-2">
                    <Label htmlFor="state">State *</Label>
                    <Controller
                      name="state"
                      control={control}
                      rules={{
                        required: "State is required",
                        validate: (value) => value !== "" || "Please select a valid state"
                      }}
                      render={({ field }) => (
                        <div>
                          <Select
                            value={watchedState || ""}
                            onValueChange={field.onChange}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select State" />
                            </SelectTrigger>
                            <SelectContent>
                              {allStatesList?.map((state) => (
                                <SelectItem
                                  key={state.value}
                                  value={state.value.toString()}
                                >
                                  {state.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          {errors.state && (
                            <p className="text-red-500 text-sm mt-1">
                              {errors.state.message}
                            </p>
                          )}
                        </div>
                      )}
                    />
                  </div>

                  {/* City */}
                  <div className="space-y-2">
                    <Label htmlFor="city">City *</Label>
                    <Controller
                      name="city"
                      control={control}
                      rules={{
                        required: "City is required",
                        validate: (value) => value !== "" || "Please select a valid city"
                      }}
                      render={({ field }) => (
                        <div>
                          <Select
                            value={watchedCity || ""}
                            onValueChange={field.onChange}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select City" />
                            </SelectTrigger>
                            <SelectContent>
                              {allLocationsList?.map((city) => (
                                <SelectItem
                                  key={city.value}
                                  value={city.value.toString()}
                                >
                                  {city.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          {errors.city && (
                            <p className="text-red-500 text-sm mt-1">
                              {errors.city.message}
                            </p>
                          )}
                        </div>
                      )}
                    />
                  </div>

                  {/* Zipcode */}
                  <div className="space-y-2">
                    <Label htmlFor="zipcode">Zipcode *</Label>
                    <Controller
                      name="zipcode"
                      control={control}
                      rules={{
                        required: "Zipcode is required",
                        pattern: {
                          value: /^\d{5}(-\d{4})?$/,
                          message: "Please enter a valid zipcode (e.g., 12345 or 12345-6789)"
                        },
                        validate: (value) => {
                          if (!value) return "Zipcode is required";
                          const cleaned = value.replace("-", "");
                          if (cleaned.length !== 5 && cleaned.length !== 9) {
                            return "Zipcode must be 5 or 9 digits";
                          }
                          return true;
                        }
                      }}
                      render={({ field }) => (
                        <div>
                          <Input
                            {...field}
                            id="zipcode"
                            placeholder="Enter zipcode (e.g., 12345 or 12345-6789)"
                            maxLength={10}
                            value={watchedZipcode || ""}
                          />
                          {errors.zipcode && (
                            <p className="text-red-500 text-sm mt-1">
                              {errors.zipcode.message}
                            </p>
                          )}
                        </div>
                      )}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact Details Section */}
          <Card>
            <CardHeader>
              <CardTitle>Contact Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">

                  <Controller
                    name="firstName"
                    control={control}
                    render={({ field }) => (
                      <div>
                        <label className="block text-sm font-medium">First Name</label>
                        <Input
                          {...field}
                          placeholder="Enter First name"
                          value={watchedFirstName || ""}
                        />
                        {errors.firstName && (
                          <p className="text-red-500 text-sm mt-1">{errors.firstName.message}</p>
                        )}
                      </div>
                    )}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="last-name">Last Name</Label>
                  <Input
                    id="last-name"
                    placeholder="Contact Last name"
                    value={watchedLastName || ""}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                  />
                  {errors.lastName && (
                    <p className="text-red-500 text-sm mt-1">{errors.lastName.message}</p>
                  )}
                </div>


                <div className="space-y-2">
                  <Label htmlFor="contact-phone">Mobile</Label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <Input
                      id="contact-phone"
                      placeholder="Phone number"
                      value={watchedContactPhone || ""}
                      onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  {errors.contactPhone && (
                    <p className="text-red-500 text-sm mt-1">{errors.contactPhone.message}</p>
                  )}
                </div>

                <Controller
                  name="contactEmail"
                  control={control}
                  render={({ field }) => (
                    <div>
                      <label className="block text-sm font-medium">Email</label>
                      <Input
                        //id="contact-email"
                        {...field}
                        //type="email"
                        placeholder="Email address"
                        value={watchedContactEmail || ""}
                      //onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                      />
                      {errors.contactEmail && (
                        <p className="text-red-500 text-sm mt-1">{errors.contactEmail.message}</p>
                      )}
                    </div>
                  )}
                />
                {/* <div className="space-y-2">
                  <Label htmlFor="contact-email">Email</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <Input
                      id="contact-email"
                      type="email"
                      placeholder="Email address"
                      value={watchedContactEmail || ""}
                      onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  {errors.contactEmail && (
                    <p className="text-red-500 text-sm mt-1">{errors.contactEmail.message}</p>
                  )}
                </div> */}
              </div>
            </CardContent>
          </Card>

          {/* Video Links Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Link className="w-5 h-5" />
                Video Links
              </CardTitle>
              <p className="text-sm text-gray-600">Add up to 5 video links for your announcement</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="text-right">
                  <Button
                    type="button"
                    onClick={() => append({ label: '', videoUrl: '' })}
                    disabled={fields.length >= 5}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    Add Video Link ({fields.length}/5)
                  </Button>
                </div>

                {fields.map((field, index) => (
                  <div key={field.id} className="grid grid-cols-1 md:grid-cols-2 gap-4 border p-4 rounded-lg bg-gray-50 relative">
                    <div>
                      <Label>Label</Label>
                      <Controller
                        name={`videos.${index}.label`}
                        control={control}
                        render={({ field }) => (
                          <Input {...field} placeholder="e.g., Training Session" maxLength={50} />
                        )}
                      />
                      <div className="text-xs text-gray-500">
                        {(field.label || '').length}/50
                      </div>
                      {errors.videos?.[index]?.label && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.videos[index]?.label?.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label>Video URL</Label>
                      <Controller
                        name={`videos.${index}.videoUrl`}
                        control={control}
                        render={({ field }) => (
                          <Input {...field} type="url" placeholder="https://youtube.com/..." />
                        )}
                      />
                      {errors.videos?.[index]?.videoUrl && (
                        <p className="text-red-500 text-sm mt-1">
                          {errors.videos[index]?.videoUrl?.message}
                        </p>
                      )}
                    </div>

                    {fields.length > 1 && (
                      <Button
                        type="button"
                        variant="ghost"
                        className="absolute top-2 right-2 text-red-500"
                        onClick={() => remove(index)}
                      >
                        Remove
                      </Button>
                    )}
                  </div>
                ))}

                {typeof errors.videos === 'object' && !Array.isArray(errors.videos) && errors.videos?.message && (
                  <p className="text-red-500 text-sm">{errors.videos?.message}</p>
                )}
              </div>
            </CardContent>


          </Card>

          {/* Action Buttons */}
          <div className="flex justify-center gap-4 pt-8">
            <Button
              type="button"
              variant="outline"
              onClick={handleBackToProfile}
              className="px-8"
            >
              Back To Profile
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="px-8 bg-blue-600 hover:bg-blue-700"
            >
              {isSubmitting ? "Creating..." : "Announcements"}
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleCancel}
              className="px-8"
            >
              Cancel
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddAnnouncementPage;
