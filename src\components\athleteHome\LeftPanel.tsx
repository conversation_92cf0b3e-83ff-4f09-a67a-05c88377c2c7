"use client";
import { useLocalStoredInfo } from "@/hooks/useLocalStoredInfo";
import { useTokenValues } from "@/hooks/useTokenValues";
import { AppDispatch, RootState } from "@/store";
import { fetchAthleteIntro } from "@/store/slices/athlete/athleteProfileSlice";
import Link from "next/link";
import { useEffect, useState } from "react";
import { WiStars } from "react-icons/wi";
import { useDispatch, useSelector } from "react-redux";
import { Button } from "../ui/button";

export default function LeftPanel() {
  const { profileCardData } = useSelector((state: RootState) => state.athleteProfile)
  const { profileData } = useLocalStoredInfo()
  const { isPremiumUser, userId } = useTokenValues()
  const dispatch = useDispatch<AppDispatch>()

  const initialFetches = async () => {
    await dispatch(fetchAthleteIntro())
  }

  useEffect(() => {
    initialFetches()
  }, [dispatch])

  const [favourites, setFavourites] = useState({
    coaches: ["<PERSON> Miller", "Dr. Speed"],
    business: ["Track Nation", "Pro Athletics Club"],
    specialOffers: ["10% off Nike Gear", "Free Physiotherapy Session"],
    resources: ["Running Form Guide", "Weekly Training Plan"],
  });

  return (
    <div className="space-y-4">
      {/* === Container 1: Profile Box + Primary Sports === */}
      <div className="bg-white rounded-lg shadow p-4 space-y-4">
        {/* Profile Box */}
        <div className="bg-gradient-to-r from-pink-200 to-blue-200 rounded-lg p-4 flex flex-col items-center">
          {userId && isPremiumUser ?
            <div className="mb-2 inline-block px-3 py-1 text-wrap text-center text-xs font-extrabold text-yellow-800 bg-yellow-300 rounded-full shadow-lg border-4 border-white">
              <div className="flex items-center">
                <WiStars className="text-yellow-700 text-3xl" size={22} /> Premium Access
              </div>
            </div> :
            <Link href={`/athlete/premium-plan`}>
              <Button>
                Upgrade to premium
              </Button>
            </Link>
          }
          <div className="bg-gradient-to-tr from-indigo-300 to-blue-200 mt-3 p-2 rounded-full">
            <img
              src={profileCardData?.profileImage || "/user.svg"}
              alt="Profile"
              loading="lazy"
              className="w-24 h-24 rounded-full object-cover"
              onError={(e) => e.currentTarget.src = "/user.svg"}
            />
          </div>
          <h2 className="text-lg font-semibold mt-3">{(profileCardData?.firstName || profileData?.userFirstName) + " " + (profileCardData?.lastName || profileData?.userLastName || '')}</h2>
          <p className="text-sm text-gray-500 text-center">
            {profileCardData?.blurb || profileData?.bio}
          </p>
        </div>

        {/* Primary Sports */}
        <div>
          <h3 className="text-sm font-semibold text-gray-700 mb-2 text-center">
            Primary Sports
          </h3>
          <ul className="text-sm space-y-2">
            {profileCardData?.primarySportsList?.length! > 0 ? profileCardData?.primarySportsList?.map(each =>
              <li key={each} className="flex items-center space-x-2 text-blue-600 hover:underline cursor-pointer">
                <span>{each}</span>
              </li>
            ) : <p>Not Specified</p>}
          </ul>
        </div>
      </div>

      {/* === Container 2: Favourites Box === */}
      <div className="bg-white rounded-lg shadow p-4 space-y-3">
        <h3 className="text-sm font-semibold text-gray-700 mb-2">⭐ Saved Vault</h3>

        <ul className="space-y-2">
          {Object.entries(favourites).map(([group, items]) => {
            // Group icon and readable label
            const groupInfo: Record<string, { icon: string; label: string }> = {
              coaches: { icon: "🎯", label: "Coaches" },
              business: { icon: "🏢", label: "Business/Orgs" },
              specialOffers: { icon: "🎁", label: "Special Offers" },
              resources: { icon: "📚", label: "Resources" },
            };

            const { icon, label } = groupInfo[group] || { icon: "📌", label: group };

            return (
              <li
                key={group}
                className="flex items-center justify-between text-sm text-gray-800 hover:bg-gray-100 p-2 rounded cursor-pointer"
              >
                <div className="flex items-center space-x-2">
                  <span className="text-lg">{icon}</span>
                  <span>{label}</span>
                </div>
                <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full">
                  {items.length}
                </span>
              </li>
            );
          })}
        </ul>
      </div>

    </div>
  );
}
