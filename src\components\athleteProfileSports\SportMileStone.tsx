'use client'
import AddMileStoneVicoryVault from "@/components/common/AddMileStoneVicoryVault";
import { Switch } from "@/components/ui/switch";
import { useHandleAthleteSectionExpose } from "@/hooks/useAthleteExposeSections";
import { useLocalStoredInfo } from "@/hooks/useLocalStoredInfo";
import { useTokenValues } from "@/hooks/useTokenValues";
import { AppDispatch, RootState } from "@/store";
import { deleteAthleteSportMilestone, fetchAthleteSportMilestones, handleUpdateUserInput, postAthleteSportMileStones, putAthleteSportMilestones } from "@/store/slices/athlete/athleteSportProfileSlice";
import { EachMileStoneVictoryItem } from "@/utils/interfaces";
import { format } from "date-fns";
import { Params } from "next/dist/shared/lib/router/utils/route-matcher";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { WiStars } from "react-icons/wi";
import { useDispatch, useSelector } from "react-redux";
import MileStoneItem from "../common/MileStoneItem";
import { Button } from "../ui/button";

interface IProps {
    params: Params
}
const SportMileStone = ({ params }: IProps) => {
    const { mileStoneId, toggleMileStone, isAddMileStone, mileStoneData, addedMileStonesList, apiStatus } = useSelector((state: RootState) => state.athleteSportProfile);
    const dispatch = useDispatch<AppDispatch>()
    const { userId, roleId, isPremiumUser } = useTokenValues()
    const { profileId, } = useLocalStoredInfo()
    const decodedSportName = decodeURIComponent(params?.sportName);
    const { handleAthleteSportsSectionsHide } = useHandleAthleteSectionExpose()
    const router = useRouter()

    useEffect(() => {
        dispatch(fetchAthleteSportMilestones(params?.sportId))
    }, [dispatch])

    const handleToggleSection = (checked: boolean) => {
        handleAthleteSportsSectionsHide(params?.sportId, 'toggleMileStone', checked)
    }

    const handleAddModal = () => {
        dispatch(handleUpdateUserInput({ name: 'mileStoneId', value: null }))
        dispatch(handleUpdateUserInput({ name: 'isAddMileStone', value: !isAddMileStone }))
        dispatch(handleUpdateUserInput({ name: 'mileStoneData', value: null }))
    }

    const handleSaveMileStone = async (data: Partial<EachMileStoneVictoryItem> | null) => {
        const payload = {
            roleId,
            athleteId: profileId,
            userId,
            sportId: Number(params?.sportId),
            milestoneTitle: data?.title,
            milestoneBlurb: data?.blurb,
            milestoneDesc: null,
            milestoneUrl: data?.link,
            milestoneDate: data?.date ? format(new Date(data?.date), 'yyyy-MM-dd') : null,
            genTagId1: data?.tags?.length! > 0 ? data?.tags?.[0]?.value : null,
            genTagId2: data?.tags?.length! > 1 ? data?.tags?.[1]?.value : null,
            genTagId3: data?.tags?.length! > 2 ? data?.tags?.[2]?.value : null,
            genTagId4: null,
            genTagId5: null,
            genTagId6: null,
            mediaType: null,
            mediaUrl: data?.file || null,
            isHidden: false,
        }

        try {
            if (mileStoneId) {
                const resultAction = await dispatch(putAthleteSportMilestones({ payload, mileStoneId }))
                if (putAthleteSportMilestones.fulfilled.match(resultAction)) {
                    await dispatch(fetchAthleteSportMilestones(params?.sportId))
                    handleAddModal()
                }
            } else {
                const resultAction = await dispatch(postAthleteSportMileStones(payload))
                if (postAthleteSportMileStones.fulfilled.match(resultAction)) {
                    await dispatch(fetchAthleteSportMilestones(params?.sportId))
                    dispatch(handleUpdateUserInput({ name: 'isAddMileStone', value: false }))
                }
            }
        } catch (error) {
            console.log(error)
        }
    }

    const handleEditMileStone = (id: number) => {
        dispatch(handleUpdateUserInput({ name: 'mileStoneId', value: id }))
        const pickedData = addedMileStonesList?.find(each => each?.id === id)
        dispatch(handleUpdateUserInput({ name: 'isAddMileStone', value: true }))
        dispatch(handleUpdateUserInput({ name: 'mileStoneData', value: { ...pickedData, date: pickedData?.date && new Date(pickedData?.date) } }))
    }

    const handleDeleteMileStone = async (id) => {
        try {
            const resultAction = await dispatch(deleteAthleteSportMilestone(id))
            if (deleteAthleteSportMilestone.fulfilled.match(resultAction)) {
                await dispatch(fetchAthleteSportMilestones(params?.sportId))
            }
        } catch (error) {
            console.log(error)
        }
    }

    const handleUpgradeToPremium = () => {
        router.push(`/athlete/premium-plan`)
    }

    return (
        <>
            <div className="bg-slate-100 rounded-lg p-4 space-y-5">
                {isPremiumUser ? <>
                    <div className="flex items-center justify-center gap-5">
                        <h3 className="text-xl font-bold">{decodedSportName ?? `${decodedSportName} - `} Key Milestones</h3>
                        <Switch checked={toggleMileStone} onCheckedChange={handleToggleSection} />
                    </div>
                    {toggleMileStone ? <>
                        <div className="flex items-end justify-end">
                            <AddMileStoneVicoryVault
                                origin="Milestone"
                                open={isAddMileStone}
                                handleAddModal={handleAddModal}
                                sourceData={mileStoneData}
                                handleSaveForm={handleSaveMileStone}
                                loading={apiStatus === 'sportMilestonePending'}
                                isEdit={Boolean(mileStoneId)}
                            />
                        </div>
                        {addedMileStonesList?.length ?
                            <div className="space-y-8">
                                {addedMileStonesList?.map((item, index) => (
                                    <MileStoneItem
                                        item={item}
                                        key={item?.id + 'milestone'}
                                        handleEditMileStone={handleEditMileStone}
                                        handleDeleteMileStone={handleDeleteMileStone}
                                    />
                                ))}
                            </div>
                            :
                            <div className="flex items-center justify-center py-5">
                                <p className="text-center text-gray-600 text-sm w-3/4">
                                    No key milestones added — start tracking your {decodedSportName} journey!
                                </p>
                            </div>
                        }
                    </> : null}
                </> : <>
                    <div className="flex flex-col items-center justify-center p-6 border border-dashed border-yellow-400 rounded-xl bg-yellow-50 gap-4 text-center">
                        <div className="flex items-center gap-2">
                            <WiStars className="text-yellow-500 text-3xl" />
                            <h3 className="text-2xl font-bold text-gray-800">
                                {decodedSportName ?? `${decodedSportName} - `} Key Milestones
                            </h3>
                        </div>
                        <p className="text-gray-600 text-sm max-w-md">
                            Unlock exclusive insights and showcase your key milestones with a{" "}
                            <span className="font-semibold text-yellow-600">Premium subscription</span>.
                            Upgrade now and stand out!
                        </p>
                        <Button onClick={handleUpgradeToPremium} className="px-6 py-2 bg-yellow-500 text-white font-semibold rounded-full shadow-md hover:bg-yellow-600 transition-all">
                            Upgrade to Premium
                        </Button>
                    </div>

                </>}
            </div>
        </>
    )
}
export default SportMileStone