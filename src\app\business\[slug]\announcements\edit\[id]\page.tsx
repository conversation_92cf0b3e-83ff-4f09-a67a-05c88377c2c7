"use client";

import React, { useState, useEffect } from "react";
import { useRout<PERSON>, useParams } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { useForm, useFieldArray, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { AppDispatch, RootState } from "@/store";
import { fetchAllStates, fetchAllLocations } from "@/store/slices/commonSlice";
import {
  fetchAnnouncementsCategory,
  fetchAnnouncementType,
  fetchAnnouncementById,
  updateAnnouncement,
  imageAnnouncement
} from "@/store/slices/org-announcement/announcement";
import { useSnackbar } from "@/contexts/SnackbarContext";
import { Button } from "@/components/ui/button";
import { ArrowLeft, CalendarIcon, LinkIcon, Mail, MapPin, Phone, Save, Upload, X } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { AnnouncementFormData } from "../../add/page";
import { BusinessRoutes } from "@/utils/businessRoutes";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import DateRangePicker from "@/components/common/DatePicker";
import { channelsList } from "@/utils/constantDropdown";
import { EachSearchItem } from "@/utils/interfaces";

interface State {
  id: number;
  name: string;
  stateCode: string;
}

interface City {
  id: number;
  name: string;
}

interface AnnouncementType {
  id: number;
  typeName: string;
}

interface AnnouncementCategory {
  id: number;
  categoryName: string;
}

interface VideoLink {
  label: string;
  videoUrl: string;
}

interface DateRange {
  start: Date | null;
  end: Date | null;
}

// Validation Schema
const validationSchema = yup.object().shape({
  title: yup
    .string()
    .required("Title is required")
    .min(3, "Title must be at least 3 characters")
    .max(100, "Title must not exceed 100 characters"),

  fullContent: yup
    .string()
    .required("Announcement Details is required")
    .min(10, "Announcement Details must be at least 10 characters")
    .max(500, "Announcement Details must not exceed 500 characters"),

  image: yup.string().nullable(),

  dateRange: yup.object().shape({
    start: yup.date().nullable(),
    end: yup.date().nullable().when('start', {
      is: (start: any) => start != null,
      then: (schema) => schema.min(yup.ref('start'), "End date must be after start date"),
      otherwise: (schema) => schema
    })
  }).nullable(),

  area: yup.string().nullable(),

  type: yup
    .string()
    .required("Type is required"),

  category: yup
    .string()
    .required("Category is required"),

  announcementFormat: yup
    .string()
    .required("Announcement format is required"),

  registrationLink: yup
    .string()
    .required("Please enter a valid URL")
    .nullable()
    .test('url', 'Please enter a valid URL', function (value) {
      if (!value || value.trim() === '') return true;
      return /^https?:\/\/.+/.test(value);
    }),

  address: yup
    .string()
    .nullable()
    .max(200, "Address must not exceed 200 characters"),

  state: yup.string().nullable(),

  city: yup.string().nullable(),

  zipcode: yup
    .string()
    .nullable()
    .test('zipcode', 'Zipcode must be 5-6 digits', function (value) {
      if (!value || value.trim() === '') return true;
      return /^[0-9]{5,6}$/.test(value);
    }),

  firstName: yup
    .string()
    .required("first Name is required")
    .min(3, "first Name must be at least 3 characters")
    .max(10, "first Name must not exceed 10 characters"),

  lastName: yup
    .string()
    .required("last Name is required")
    .min(3, "last Name must be at least 3 characters")
    .max(10, "last Name must not exceed 10 characters"),

  contactPhone: yup
    .string()
    .required("Phone Number is required")
    .matches(
      /^(\d\s?){10}$/,
      "Phone Number must be 10 digit number"
    ),

  contactEmail: yup
    .string()
    .email("Invalid Email")
    .required("Email Address Is Required"),

  videos: yup.array()
    .of(
      yup.object().shape({
        label: yup.string().required('Video label is required'),
        videoUrl: yup.string().url('Must be a valid URL').required('Video URL is required'),
      })
    )
    .min(1, 'At least one video is required')
    .max(5, 'Up to 5 videos only')
    .required('Videos are required'),

  isPublished: yup.boolean().required()
});

const AnnouncementEdit: React.FC = () => {
  const { id, slug } = useParams<{ id: string; slug: string }>();
  const router = useRouter();
  const params = useParams<{ slug: string }>();
  const dispatch = useDispatch<AppDispatch>();
  const { showSuccess, showError } = useSnackbar();
  const [loading, setLoading] = useState(true);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  // Common slice state for states and locations
  const {
    allStatesList,
    allLocationsList,
  } = useSelector((state: RootState) => state.commonSlice);

  const businessInfo = BusinessRoutes.parseSlug(params.slug as string);
  const {
    annoucementType,
    selectedAnnouncement,
    orgAnnouncement: announcementCategories,
    imageData
  } = useSelector((state: RootState) => state.announcementDashboard);

  const { orgProfile } = useSelector((state: RootState) => state.orgProfile);

  // React Hook Form setup
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
    reset,
  } = useForm<AnnouncementFormData>({
    resolver: yupResolver(validationSchema) as any,
    mode: 'onChange',
    defaultValues: {
      title: "",
      fullContent: "",
      image: null,
      dateRange: null,
      area: null,
      type: "",
      category: "",
      announcementFormat: "",
      registrationLink: "",
      address: "",
      state: null,
      city: null,
      zipcode: "",
      firstName: "",
      lastName: "",
      contactPhone: "",
      contactEmail: "",
      videos: [{ label: '', videoUrl: '' }],
      isPublished: false,
    }
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'videos',
  });

  // Watch form values
  const watchedState = watch("state");
  const watchedType = watch("type");
  const watchedCategory = watch("category");

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        await Promise.all([
          dispatch(fetchAnnouncementType()),
          dispatch(fetchAnnouncementsCategory()),
          dispatch(fetchAllStates()),
          dispatch(fetchAnnouncementById(id)),
        ]);
        setLoading(false);
      } catch (error) {
        console.error("Error loading data:", error);
        setLoading(false);
      }
    };

    loadInitialData();
  }, [id, dispatch]);

  // Load cities when state changes
  useEffect(() => {
    if (watchedState) {
      dispatch(fetchAllLocations(parseInt(watchedState)));
    }
  }, [watchedState, dispatch]);

  // Populate form when announcement data is loaded
  useEffect(() => {
    if (selectedAnnouncement && !loading) {
      const formatOption = channelsList.find(o => o.value === selectedAnnouncement.announcementFormat);

      const formData: AnnouncementFormData = {
        title: selectedAnnouncement.title || "",
        fullContent: selectedAnnouncement.fullContent || "",
        image: selectedAnnouncement.imageUrl || null,
        dateRange: selectedAnnouncement.startDate && selectedAnnouncement.endDate ? {
          start: new Date(selectedAnnouncement.startDate),
          end: new Date(selectedAnnouncement.endDate)
        } : null,
        area: selectedAnnouncement.area || null,
        type: selectedAnnouncement?.type?.id?.toString() || "",
        category: selectedAnnouncement?.category?.id?.toString() || "",
        announcementFormat: formatOption?.value || "",
        registrationLink: selectedAnnouncement.registrationLink || "",
        address: selectedAnnouncement?.address || "",
        state: selectedAnnouncement?.stateId?.id ? selectedAnnouncement.stateId.id.toString() : null,
        city: selectedAnnouncement?.cityId?.id ? selectedAnnouncement.cityId.id.toString() : null,
        zipcode: selectedAnnouncement?.zipCode || "",
        firstName: selectedAnnouncement?.firstName || "",
        lastName: selectedAnnouncement?.lastName || "",
        contactPhone: selectedAnnouncement?.phone || "",
        contactEmail: selectedAnnouncement?.email || "",
        videos: selectedAnnouncement.videoLinks?.length ? selectedAnnouncement.videoLinks : [{ label: '', videoUrl: '' }],
        isPublished: selectedAnnouncement.isPinned || false,
      };

      setImagePreview(selectedAnnouncement.imageUrl || null);
      reset(formData);
    }
  }, [selectedAnnouncement, loading, reset]);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const allowedTypes = ['image/png', 'image/jpg', 'image/jpeg'];
      if (!allowedTypes.includes(file.type)) {
        showError("Please select a valid image file (PNG, JPG, JPEG).", "Invalid File Type");
        return;
      }

      const maxSize = 5 * 1024 * 1024;
      if (file.size > maxSize) {
        showError("Image size should be less than 5MB.", "File Too Large");
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const base64String = e.target?.result as string;
        const clean = base64String.replace(/^data:[^;]+;base64,/, "");
        const byteChars = atob(clean);
        const byteNumbers = new Array(byteChars.length).fill(0).map((_, i) => byteChars.charCodeAt(i));
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: file.type });

        const formData = new FormData();
        formData.append("image", blob, file.name);
        if (base64String) {
          dispatch(imageAnnouncement(formData));
        }

        setValue('image', base64String);
        setImagePreview(base64String);
      };
      reader.onerror = () => {
        showError("Error reading the image file.", "Upload Error");
      };
      reader.readAsDataURL(file);
    }
  };

  const handleCancel = () => {
    const announcementsUrl = BusinessRoutes.announcements(businessInfo.businessName, businessInfo.userId);
    router.push(announcementsUrl);
  };

  const onSubmit = async (data: AnnouncementFormData): Promise<void> => {
    try {
      const updateData = {
        id: parseInt(id),
        organizationId: orgProfile[0]?.id || 16,
        orgUserId: orgProfile[0]?.userId || 444,
        announceTypeId: parseInt(data.type),
        announceCategoryId: data.category ? parseInt(data.category) : null,
        title: data.title,
        announcementFormat: data.announcementFormat,
        fullContent: data.fullContent,
        startDate: data.dateRange && data.dateRange.start ? data.dateRange.start.toISOString().split("T")[0] : null,
        endDate: data.dateRange && data.dateRange.end ? data.dateRange.end.toISOString().split("T")[0] : null,
        isPinned: data.isPublished,
        location: {
          address: data.address,
          stateId: data.state ? parseInt(data.state) : null,
          cityId: data.city ? parseInt(data.city) : null,
          zipCode: data.zipcode,
        },
        contact: {
          firstName: data.firstName,
          lastName: data.lastName,
          email: data.contactEmail,
          phone: data.contactPhone
        },
        registrationLink: data.registrationLink,
        image: imageData?.imageUrl || null,
        video: data.videos?.filter(v => v.label && v.videoUrl) || []
      };

      const result = await dispatch(updateAnnouncement({
        id: selectedAnnouncement?.id as number,
        updatedData: updateData
      }));

      if (updateAnnouncement.fulfilled.match(result)) {
        showSuccess("Announcement updated successfully!", "Success");
        const announcementsUrl = BusinessRoutes.announcements(businessInfo.businessName, businessInfo.userId);
        router.push(announcementsUrl);
      } else {
        showError("Failed to update announcement. Please try again.", "Error");
      }
    } catch (error) {
      console.error("Error updating announcement:", error);
      showError("An error occurred while updating the announcement.", "Error");
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-card">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={() => handleCancel()}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Announcements
              </Button>
              <div className="h-6 w-px bg-border" />
              <h1 className="text-xl font-semibold">Edit Announcement</h1>
            </div>
            <Controller
              name="isPublished"
              control={control}
              render={({ field }) => (
                <div className="flex items-center gap-2">
                  <Label htmlFor="publish-toggle" className="text-sm font-medium">
                    Published
                  </Label>
                  <Switch
                    id="publish-toggle"
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </div>
              )}
            />
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
          {/* Title and Image Section */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Title and Content */}
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Announcement Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Controller
                    name="title"
                    control={control}
                    render={({ field }) => (
                      <div>
                        <Label htmlFor="title">Title</Label>
                        <Input
                          id="title"
                          {...field}
                          placeholder="Enter announcement title"
                          className="text-lg"
                        />
                        {errors.title && (
                          <p className="text-destructive text-sm mt-1">{errors.title.message}</p>
                        )}
                      </div>
                    )}
                  />

                  <Controller
                    name="fullContent"
                    control={control}
                    render={({ field }) => (
                      <div>
                        <Label htmlFor="content">Content</Label>
                        <Textarea
                          id="fullContent"
                          {...field}
                          placeholder="Enter announcement content..."
                          maxLength={500}
                          rows={6}
                          className="resize-none"
                        />
                        <div className="text-right text-sm text-muted-foreground mt-2">
                          {field.value?.length || 0}/500
                        </div>
                        {errors.fullContent && (
                          <p className="text-destructive text-sm mt-1">{errors.fullContent.message}</p>
                        )}
                      </div>
                    )}
                  />
                </CardContent>
              </Card>
            </div>

            {/* Image Upload */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle>Image</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="border-2 border-dashed border-border rounded-lg p-6 text-center hover:border-primary/50 transition-colors">
                    {imagePreview ? (
                      <div className="space-y-4">
                        <img
                          src={imagePreview}
                          alt="Preview"
                          className="w-full h-32 object-cover rounded-lg"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => {
                            setImagePreview(null);
                            setValue('image', null);
                          }}
                        >
                          Remove Image
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <Upload className="w-12 h-12 text-muted-foreground mx-auto" />
                        <div>
                          <Label htmlFor="image-upload" className="cursor-pointer">
                            <span className="text-primary hover:text-primary/80">Upload Image</span>
                            <span className="text-muted-foreground"> - png, jpg, jpeg</span>
                          </Label>
                          <Input
                            id="image-upload"
                            type="file"
                            accept="image/png,image/jpg,image/jpeg"
                            onChange={handleImageUpload}
                            className="hidden"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Date Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CalendarIcon className="w-5 h-5" />
                Event Dates
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Controller
                    name="dateRange"
                    control={control}
                    render={({ field, fieldState }) => {
                      const { onChange, value } = field;
                      return (
                        <div className="space-y-2">
                          <Label>Date Range</Label>
                          <DateRangePicker
                            startDate={value?.start ?? null}
                            endDate={value?.end ?? null}
                            onDateChange={(dates: [Date | null, Date | null]) => {
                              const [start, end] = dates;
                              onChange({ start, end });
                            }}
                          />
                          {errors.dateRange && (
                            <p className="text-red-500 text-sm mt-1">{errors.dateRange.message}</p>
                          )}
                        </div>
                      );
                    }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Classification */}
          <Card>
            <CardHeader>
              <CardTitle>Classification</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Controller
                  name="type"
                  control={control}
                  render={({ field }) => (
                    <div className="space-y-2">
                      <Label>Type</Label>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select Type" />
                        </SelectTrigger>
                        <SelectContent>
                          {annoucementType.data?.map((type: AnnouncementType) => (
                            <SelectItem key={type.id} value={type.id.toString()}>
                              {type.typeName}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.type && (
                        <p className="text-destructive text-sm mt-1">{errors.type.message}</p>
                      )}
                    </div>
                  )}
                />

                <Controller
                  name="category"
                  control={control}
                  render={({ field }) => (
                    <div className="space-y-2">
                      <Label>Category</Label>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select Category" />
                        </SelectTrigger>
                        <SelectContent>
                          {announcementCategories?.data?.map((category: AnnouncementCategory) => (
                            <SelectItem key={category.id} value={category.id.toString()}>
                              {category.categoryName}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.category && (
                        <p className="text-destructive text-sm mt-1">{errors.category.message}</p>
                      )}
                    </div>
                  )}
                />

                <Controller
                  name="announcementFormat"
                  control={control}
                  render={({ field }) => (
                    <div className="space-y-2">
                      <Label>Format</Label>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select Format" />
                        </SelectTrigger>
                        <SelectContent>
                          {channelsList.map((format) => (
                            <SelectItem key={format.value} value={format.value}>
                              {format.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.announcementFormat && (
                        <p className="text-destructive text-sm mt-1">{errors.announcementFormat.message}</p>
                      )}
                    </div>
                  )}
                />
              </div>

              <div className="mt-6">
                <Controller
                  name="registrationLink"
                  control={control}
                  render={({ field }) => (
                    <div className="space-y-2">
                      <Label>Registration Link</Label>
                      <Input
                        {...field}
                        type="url"
                        placeholder="https://example.com/register"
                        value={field.value || ""}
                      />
                      {errors.registrationLink && (
                        <p className="text-destructive text-sm mt-1">{errors.registrationLink.message}</p>
                      )}
                    </div>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Location */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="w-5 h-5" />
                Location
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <Controller
                name="address"
                control={control}
                render={({ field }) => (
                  <div className="space-y-2">
                    <Label>Address</Label>
                    <Textarea
                      {...field}
                      placeholder="Enter full address"
                      rows={2}
                      value={field.value || ""}
                    />
                    {errors.address && (
                      <p className="text-destructive text-sm mt-1">{errors.address.message}</p>
                    )}
                  </div>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Controller
                  name="state"
                  control={control}
                  render={({ field }) => (
                    <div className="space-y-2">
                      <Label>State</Label>
                      <Select
                        value={field.value ? field.value.toString() : ""} // Ensure string comparison
                        onValueChange={(value) => {
                          field.onChange(value);
                          setValue("city", null);
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select State" />
                        </SelectTrigger>
                        <SelectContent>
                          {allStatesList.map((state: EachSearchItem) => (
                            <SelectItem
                              key={state.value}
                              value={state.value.toString()}
                            >
                              {state.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                />

                <Controller
                  name="city"
                  control={control}
                  render={({ field }) => (
                    <div className="space-y-2">
                      <Label>City</Label>
                      <Select
                        value={field.value ? field.value.toString() : ""} // Ensure string comparison
                        onValueChange={field.onChange}
                        disabled={!watchedState}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select City" />
                        </SelectTrigger>
                        <SelectContent>
                          {allLocationsList
                            .filter(city => watchedState ? selectedAnnouncement?.stateId?.id === selectedAnnouncement?.stateId?.id.toString() : true)
                            .map((city: EachSearchItem) => (
                              <SelectItem
                                key={city.value}
                                value={city.value.toString()}
                              >
                                {city.label}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                />

                <Controller
                  name="zipcode"
                  control={control}
                  render={({ field }) => (
                    <div className="space-y-2">
                      <Label>ZIP Code</Label>
                      <Input
                        {...field}
                        placeholder="12345"
                        value={field.value || ""}
                      />
                      {errors.zipcode && (
                        <p className="text-destructive text-sm mt-1">{errors.zipcode.message}</p>
                      )}
                    </div>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Contact Details */}
          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Controller
                  name="firstName"
                  control={control}
                  render={({ field }) => (
                    <div className="space-y-2">
                      <Label>First Name</Label>
                      <Input
                        {...field}
                        placeholder="Contact first name"
                        value={field.value || ""}
                      />
                      {errors.firstName && (
                        <p className="text-destructive text-sm mt-1">{errors.firstName.message}</p>
                      )}
                    </div>
                  )}
                />

                <Controller
                  name="lastName"
                  control={control}
                  render={({ field }) => (
                    <div className="space-y-2">
                      <Label>Last Name</Label>
                      <Input
                        {...field}
                        placeholder="Contact last name"
                        value={field.value || ""}
                      />
                      {errors.lastName && (
                        <p className="text-destructive text-sm mt-1">{errors.lastName.message}</p>
                      )}
                    </div>
                  )}
                />

                <Controller
                  name="contactPhone"
                  control={control}
                  render={({ field }) => (
                    <div className="space-y-2">
                      <Label>Phone</Label>
                      <div className="relative">
                        <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                        <Input
                          {...field}
                          placeholder="(*************"
                          className="pl-10"
                          value={field.value || ""}
                        />
                      </div>
                      {errors.contactPhone && (
                        <p className="text-destructive text-sm mt-1">{errors.contactPhone.message}</p>
                      )}
                    </div>
                  )}
                />

                <Controller
                  name="contactEmail"
                  control={control}
                  render={({ field }) => (
                    <div className="space-y-2">
                      <Label>Email</Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                        <Input
                          {...field}
                          type="email"
                          placeholder="<EMAIL>"
                          className="pl-10"
                          value={field.value || ""}
                        />
                      </div>
                      {errors.contactEmail && (
                        <p className="text-destructive text-sm mt-1">{errors.contactEmail.message}</p>
                      )}
                    </div>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Video Links */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LinkIcon className="w-5 h-5" />
                Video Links
              </CardTitle>
              <p className="text-sm text-muted-foreground">Add up to 5 video links</p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-right">
                <Button
                  type="button"
                  onClick={() => append({ label: '', videoUrl: '' })}
                  disabled={fields.length >= 5}
                  variant="outline"
                >
                  Add Video ({fields.length}/5)
                </Button>
              </div>

              {fields.map((field, index) => (
                <div key={field.id} className="grid grid-cols-1 md:grid-cols-2 gap-4 border border-border p-4 rounded-lg bg-muted/30 relative">
                  <Controller
                    name={`videos.${index}.label`}
                    control={control}
                    render={({ field }) => (
                      <div className="space-y-2">
                        <Label>Label</Label>
                        <Input {...field} placeholder="e.g., Event Preview" maxLength={50} />
                        {errors.videos?.[index]?.label && (
                          <p className="text-destructive text-sm">
                            {errors.videos[index]?.label?.message}
                          </p>
                        )}
                      </div>
                    )}
                  />

                  <Controller
                    name={`videos.${index}.videoUrl`}
                    control={control}
                    render={({ field }) => (
                      <div className="space-y-2">
                        <Label>Video URL</Label>
                        <Input {...field} type="url" placeholder="https://youtube.com/..." />
                        {errors.videos?.[index]?.videoUrl && (
                          <p className="text-destructive text-sm">
                            {errors.videos[index]?.videoUrl?.message}
                          </p>
                        )}
                      </div>
                    )}
                  />

                  {fields.length > 1 && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute top-2 right-2 text-destructive hover:text-destructive"
                      onClick={() => remove(index)}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-center gap-4 pt-8">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              className="px-8"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="px-8"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-foreground mr-2"></div>
                  Updating...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Update Announcement
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AnnouncementEdit;
