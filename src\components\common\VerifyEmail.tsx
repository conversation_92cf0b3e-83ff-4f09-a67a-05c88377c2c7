import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { InputOTP, InputOTPGroup, InputOTPSlot } from "@/components/ui/input-otp";
import { Label } from "@/components/ui/label";
import { useTokenValues } from "@/hooks/useTokenValues";
import { AppDispatch, RootState } from "@/store";
import { putSubmitOTP } from "@/store/slices/auth/loginSlice";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";
import { Controller, useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import * as z from "zod";

const otpSchema = z.object({
    otp: z.string().min(6, "OTP must be 6 digits"),
});

type OtpFormData = z.infer<typeof otpSchema>;

interface IProps {
    openModal: boolean;
    handleOpen: () => void;
    email: string;
    handleClose: () => void
}

export default function VerifyEmailOTP({ openModal, email, handleOpen, handleClose }: IProps) {
    const dispatch = useDispatch<AppDispatch>()
    const { roleId, userId } = useTokenValues()
    const { apiStatus } = useSelector((state: RootState) => state.commonSlice)

    const {
        control,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm<OtpFormData>({
        resolver: zodResolver(otpSchema),
        defaultValues: { otp: "" },
    });

    const handleDialogChange = (isOpen: boolean) => {
        if (!isOpen) {
            reset();
            handleClose();
        }
    };

    const onSubmit = async (data: OtpFormData) => {
        if (!userId || !roleId) return

        const payload = {
            userId,
            roleId,
            otp: data?.otp
        }
        try {
            const resultAction = await dispatch(putSubmitOTP(payload))
            const result = resultAction.payload
            if (putSubmitOTP.fulfilled.match(resultAction)) {
                reset();
                handleClose();
            }
        } catch (error) {
            console.log(error)
        }

    };

    return (
        <Dialog open={openModal} onOpenChange={handleDialogChange}>
            <DialogContent className="max-w-sm" onInteractOutside={(event) => event.preventDefault()}>
                <DialogHeader>
                    <DialogTitle className="text-xl">Enter OTP</DialogTitle>
                    <DialogDescription>
                        We’ve sent a one-time password to your email:
                        <span className="text-secondary">{email}</span>.
                    </DialogDescription>
                </DialogHeader>

                <form
                    onSubmit={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleSubmit(onSubmit)(e);
                    }} className="space-y-5">
                    <Controller
                        control={control}
                        name="otp"
                        render={({ field }) => (
                            <>
                                <Label>One-Time Password</Label>
                                <InputOTP maxLength={6} {...field}>
                                    <InputOTPGroup className="flex items-center gap-5 justify-center my-4">
                                        <InputOTPSlot index={0} className="border border-slate-400 rounded-lg h-10 w-10" />
                                        <InputOTPSlot index={1} className="border border-slate-400 rounded-lg h-10 w-10" />
                                        <InputOTPSlot index={2} className="border border-slate-400 rounded-lg h-10 w-10" />
                                        <InputOTPSlot index={3} className="border border-slate-400 rounded-lg h-10 w-10" />
                                        <InputOTPSlot index={4} className="border border-slate-400 rounded-lg h-10 w-10" />
                                        <InputOTPSlot index={5} className="border border-slate-400 rounded-lg h-10 w-10" />
                                    </InputOTPGroup>
                                </InputOTP>

                                {errors.otp && (
                                    <p className="text-sm text-red-600">{errors.otp.message}</p>
                                )}
                            </>
                        )}
                    />

                    <Button type="submit" className="w-full">
                        {apiStatus === "otpSbmtPending" ? <Loader2 className="h-5 w-5 animate-spin" /> : 'Submit'}
                    </Button>
                </form>
            </DialogContent>
        </Dialog>
    );
}
