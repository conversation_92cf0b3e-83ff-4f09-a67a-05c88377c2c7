'use client'

import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import { useHandleAthleteSectionExpose } from "@/hooks/useAthleteExposeSections"
import { useLocalStoredInfo } from "@/hooks/useLocalStoredInfo"
import { AppDispatch, RootState } from '@/store'
import { fetchAthleteParentGuardian, handleUpdateUserInput, putAthleteParentGuardian } from '@/store/slices/athlete/athleteProfileSlice'
import { postVerifyProfileEmail } from "@/store/slices/auth/loginSlice"
import { relationShipList } from "@/store/slices/commonSlice"
import { zodResolver } from '@hookform/resolvers/zod'
import { format } from 'date-fns'
import { Loader, PencilLine } from 'lucide-react'
import { useEffect, useState } from 'react'
import { Controller, DefaultValues, useForm } from 'react-hook-form'
import { useDispatch, useSelector } from 'react-redux'
import { toast } from "react-toastify"
import * as z from 'zod'
import VerifyEmailOTP from "../common/VerifyEmail"
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Switch } from '../ui/switch'

const parentGuardianSchema = z.object({
    firstName: z.string().min(1, 'First name is required'),
    lastName: z.string().min(1, 'Last name is required'),
    phone: z.string().optional(),
    togglePhone: z.boolean().default(true),
    email: z.string().min(1, 'Email is required').email('Invalid email'),
    toggleEmail: z.boolean().default(true),
    relationship: z.string().optional(),
    toggleRelation: z.boolean().default(true),
    searchMyProfileCA: z.boolean().default(true),
})

type ParentGuardianFormValues = z.infer<typeof parentGuardianSchema>

const ParentGuardianDetails = () => {
    const { parentGuardianData, apiStatus, toggleContactSection, isVerifyEmail } = useSelector((state: RootState) => state.athleteProfile)
    const isVerifyLoading = useSelector((state: RootState) => state.login.apiStatus)
    const [isEditable, setIsEditable] = useState(true)
    const dispatch = useDispatch<AppDispatch>()
    const { handleToggleSections } = useHandleAthleteSectionExpose()
    const { userInfo } = useLocalStoredInfo()

    useEffect(() => {
        dispatch(fetchAthleteParentGuardian())
    }, [dispatch])


    const {
        control,
        handleSubmit,
        reset,
        watch,
        formState: { errors },
    } = useForm<ParentGuardianFormValues>({
        resolver: zodResolver(parentGuardianSchema),
        defaultValues: parentGuardianData as DefaultValues<ParentGuardianFormValues> ?? {
            firstName: '',
            lastName: '',
            phone: '',
            togglePhone: true,
            email: '',
            toggleEmail: true,
            relationship: '',
            toggleRelation: true,
            searchMyProfileCA: true,
        },
    })

    const togglePhone = watch('togglePhone')
    const toggleEmail = watch('toggleEmail')
    const toggleRelation = watch('toggleRelation')
    const watchedEmail = watch('email')

    useEffect(() => {
        if (parentGuardianData) {
            reset(parentGuardianData)
            setIsEditable(false)
        }
    }, [parentGuardianData, reset])

    const onSubmit = async (data: ParentGuardianFormValues) => {
        setIsEditable(false)
        const payload = {
            parentFirstName: data?.firstName,
            parentLastName: data?.lastName,
            parentPhone: data?.phone,
            parentEmail: data?.email,
            isHiddenMobile: !data?.togglePhone,
            isHiddenEmail: !data?.toggleEmail,
            makeProfilePublic: data?.searchMyProfileCA,
            primaryParentReln: data?.relationship,
            isHiddenPrimaryParentReln: !data?.toggleRelation
        }

        try {
            const resultAction = await dispatch(putAthleteParentGuardian(payload))
            if (putAthleteParentGuardian.fulfilled.match(resultAction)) {
                handleClickCancel()
                await dispatch(fetchAthleteParentGuardian())
            }
        } catch (error) {
            console.log(error)
        }
    }

    const handleClickCancel = () => {
        reset();
        setIsEditable(false)
    };

    const handleVerifyEmail = async () => {
        try {
            const resultAction = await dispatch(postVerifyProfileEmail({ email: watchedEmail }))
            const result = resultAction.payload
            if (postVerifyProfileEmail.fulfilled.match(resultAction)) {
                dispatch(handleUpdateUserInput({ name: 'isVerifyEmail', value: !isVerifyEmail }))
            }
            if (result.status !== 200 || result.status !== 201) {
                toast.error(result?.response?.data?.message)
            }
        } catch (error) {
            console.log(error)
        }
    }

    const handleCloseVerifyEmail = () => {
        dispatch(handleUpdateUserInput({ name: 'isVerifyEmail', value: false }))
    }

    return (
        <form
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-col gap-3 bg-slate-100 p-4 rounded-lg px-5"
        >
            <div className='flex items-center justify-center gap-3'>
                <h3 className="text-xl font-bold text-center">Primary Parent/Legal Guardian Contact Details</h3>
                <Switch checked={toggleContactSection} onCheckedChange={(checked) => handleToggleSections('parentGuardian', checked)} />
            </div>

            {toggleContactSection && <Button type="button" size={'icon'} variant={'outline'} className="self-end"
                onClick={() => setIsEditable(true)}>
                <PencilLine />
            </Button>}

            {toggleContactSection &&
                <>
                    {isEditable ?
                        <div className="grid grid-cols-1 md:grid-cols-2  items-start gap-8">
                            {/* First Name */}
                            <div className="flex flex-col gap-1">
                                <Label>First Name</Label>
                                <Controller
                                    name="firstName"
                                    control={control}
                                    render={({ field }) =>
                                        <Input
                                            {...field}
                                            className="border-slate-300"
                                            placeholder='Enter First Name'
                                        />}
                                />
                                {errors.firstName && <p className="text-sm text-red-600">{errors.firstName.message}</p>}
                            </div>

                            {/* Last Name */}
                            <div className="flex flex-col gap-1">
                                <Label>Last Name</Label>
                                <Controller
                                    name="lastName"
                                    control={control}
                                    render={({ field }) =>
                                        <Input
                                            {...field}
                                            className="border-slate-300"
                                            placeholder='Enter Last Name'
                                        />}
                                />
                                {errors.lastName && <p className="text-sm text-red-600">{errors.lastName.message}</p>}
                            </div>

                            {/* Contact Phone */}
                            <div className="flex flex-col gap-1">
                                <div className='flex items-center gap-2'>
                                    <Label>Contact Phone</Label>
                                    <Controller
                                        name="togglePhone"
                                        control={control}
                                        render={({ field: { value, onChange, ...rest } }) => (
                                            <Switch
                                                checked={value}
                                                onCheckedChange={onChange}
                                                {...rest}
                                            />
                                        )}
                                    />
                                </div>
                                {togglePhone && <div className='flex flex-col'>
                                    <Controller
                                        name="phone"
                                        control={control}
                                        render={({ field }) =>
                                            <Input {...field}
                                                className="border-slate-300"
                                                placeholder='Enter Contact Phone'
                                            />}
                                    />
                                    {errors.phone && <p className="text-sm text-red-600">{errors.phone.message}</p>}
                                </div>}
                            </div>

                            {/* Relationship */}
                            <div className="flex flex-col gap-1">
                                <div className='flex items-center gap-2'>
                                    <Label>Relationship to Athlete</Label>
                                    <Controller
                                        name="toggleRelation"
                                        control={control}
                                        render={({ field: { value, onChange, ...rest } }) => (
                                            <Switch
                                                checked={value}
                                                onCheckedChange={onChange}
                                                {...rest}
                                            />
                                        )}
                                    />
                                </div>

                                {toggleRelation && <div className='flex flex-col'>
                                    <Controller
                                        name="relationship"
                                        control={control}
                                        render={({ field }) => (
                                            <Select onValueChange={(option) => field.onChange(option)}
                                                value={field?.value || ''}>
                                                <SelectTrigger className="bg-white">
                                                    <SelectValue placeholder="Select Relationship" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {relationShipList?.length > 0 ? relationShipList?.map(item =>
                                                        <SelectItem key={item} value={item}>{item}</SelectItem>
                                                    ) : <SelectItem disabled value="no-options">No Options Found</SelectItem>}
                                                </SelectContent>
                                            </Select>
                                        )}
                                    />
                                    {errors.relationship && (
                                        <p className="text-sm text-red-600">{errors.relationship.message as string}</p>
                                    )}
                                </div>}
                            </div>

                            {/* Contact Email */}
                            <div className="flex flex-col gap-1">
                                <div className='flex items-center gap-2'>
                                    <Label>Contact Email</Label>
                                    <Controller
                                        name="toggleEmail"
                                        control={control}
                                        render={({ field: { value, onChange, ...rest } }) => (
                                            <Switch
                                                checked={value}
                                                onCheckedChange={onChange}
                                                {...rest}
                                            />
                                        )}
                                    />
                                </div>

                                {toggleEmail && <div className='flex flex-col'>
                                    <Controller
                                        name="email"
                                        control={control}
                                        render={({ field }) =>
                                            <Input
                                                {...field}
                                                className="border-slate-300"
                                                placeholder='Enter Contact Email'
                                            />
                                        }
                                    />
                                    {errors.email && <p className="text-sm text-red-600">{errors.email.message}</p>}
                                    {(userInfo?.email !== parentGuardianData?.email && watchedEmail !== '') ?
                                        <Button type='button' className='mt-2 self-start min-w-24' onClick={handleVerifyEmail}>
                                            {isVerifyLoading === 'verifyPending' ? <Loader className="h-5 w-5 animate-spin" /> : 'Verify My Email'}
                                        </Button> : null}
                                    {/* OTP Modal */}
                                    <VerifyEmailOTP
                                        email={watchedEmail}
                                        openModal={isVerifyEmail}
                                        handleOpen={handleVerifyEmail}
                                        handleClose={handleCloseVerifyEmail}
                                    />
                                </div>}
                            </div>

                            {/* <div className="flex items-end gap-4 mt-1">                                
                                <Button type='button' variant={'ghost'} className='mt-1 self-start underline'>Resend</Button>
                            </div> */}

                            <div className="">
                                <div className="flex items-center gap-3">
                                    <Label>Search My Profile on CA</Label>
                                    <Controller
                                        name="searchMyProfileCA"
                                        control={control}
                                        render={({ field: { value, onChange, ...rest } }) => (
                                            <Switch
                                                checked={value}
                                                onCheckedChange={onChange}
                                                {...rest}
                                            />
                                        )}
                                    />
                                </div>
                                <span className="text-sm text-secondary">
                                    (Your profile will be visible to coaches , sports
                                    businesses & orgs in Connect Athlete. If this
                                    is disabled , then your profile will not show
                                    up on the Search Assistant.)
                                </span>
                            </div>
                        </div>
                        :
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                            {/* First Name */}
                            {watch('firstName') && <div className="flex flex-col gap-1">
                                <Label>First Name</Label>
                                <div className='h-10 w-full text-ellipsis bg-white px-3 rounded-lg p-1 text-center flex items-center'>
                                    <span>{watch('firstName')}</span>
                                </div>
                            </div>}

                            {/* Last Name */}
                            {watch('lastName') && <div className="flex flex-col gap-1">
                                <Label>Last Name</Label>
                                <div className='h-10 w-full text-ellipsis bg-white px-3 rounded-lg p-1 text-center flex items-center'>
                                    <span>{watch('lastName')}</span>
                                </div>
                            </div>}

                            {/* Contact Phone */}
                            {togglePhone && watch('phone') && <div className="flex flex-col gap-1">
                                <Label>Contact Phone</Label>
                                <div className='h-10 w-full text-ellipsis bg-white px-3 rounded-lg p-1 text-center flex items-center'>
                                    <span>{watch('phone')}</span>
                                </div>
                            </div>}

                            {/* Contact Email */}
                            {toggleEmail && watch('email') && <div className="flex flex-col gap-1">
                                <Label>Contact Email</Label>
                                <div className='h-10 w-full text-ellipsis bg-white px-3 rounded-lg p-1 text-center flex items-center'>
                                    <span>{watch('email')}</span>
                                </div>
                            </div>}

                            {/* Relationship */}
                            {toggleRelation && watch('relationship') && <div className="flex flex-col gap-1">
                                <Label>Relationship to Athlete</Label>
                                <div className='h-10 w-full text-ellipsis bg-white px-3 rounded-lg p-1 text-center flex items-center'>
                                    <span>{watch('relationship')}</span>
                                </div>
                            </div>}

                            <div className="flex flex-col gap-1">
                                <Label>Search My Profile on CA</Label>
                                <div className='h-10 w-full text-ellipsis bg-white px-3 rounded-lg p-1 text-center flex items-center'>
                                    <span>{watch('searchMyProfileCA') ? 'Yes' : 'No'}</span>
                                </div>
                            </div>

                            {/* Last T&C Accepted */}
                            <div className="flex flex-col gap-1">
                                <Label>Last T&C Accepted</Label>
                                <div className='h-10 w-full text-ellipsis bg-white px-3 rounded-lg p-1 text-center flex items-center'>
                                    <span>{parentGuardianData?.lastTermsAcceptedDt ? format(new Date(parentGuardianData?.lastTermsAcceptedDt!), 'MMM, dd yyyy') : 'N/A'}</span>
                                </div>
                            </div>
                        </div>
                    }
                </>
            }

            {isEditable && toggleContactSection && (
                <div className="flex justify-end gap-4">
                    <Button variant="outline" type="button" onClick={handleClickCancel}>
                        Cancel
                    </Button>
                    <Button type='submit'>
                        {apiStatus === 'parentGuardianPending' ? (
                            <>
                                <Loader className='mr-2 h-4 w-4 animate-spin' />
                                Saving...
                            </>
                        ) : (
                            'Save'
                        )}
                    </Button>
                </div>
            )}
        </form>
    )
}

export default ParentGuardianDetails