import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuTrigger
} from "@/components/ui/dropdown-menu"
import { AppDispatch } from "@/store"
// import { logoutUser } from "@/store/slices/auth/loginSlice"
import { logout } from "@/store/slices/auth/loginSlice"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { Fragment, ReactNode } from "react"
import { useDispatch } from "react-redux"
import { toast } from "react-toastify"
import { EachNavItem } from "../NavBar"

interface IProps {
    triggerMenu: ReactNode,
    menusList?: EachNavItem[]
}

const DropdownMenusItem = ({ triggerMenu, menusList }: IProps) => {
    const router = useRouter()
    const dispatch = useDispatch<AppDispatch>()

    const handleLogout = async () => {
        try {
            const resultAction = await dispatch(logout())
            if (resultAction.type === logout.type) {
                router.push('/');
                localStorage.clear();
            }

        } catch (error) {
            toast.error("Failed to logout")
        }
    }

    const handleClickMenu = async (id: string,) => {
        if (id === "logout") {
            handleLogout()
        }
    }

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <span className="cursor-pointer">
                    {triggerMenu}
                </span>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-full">
                <DropdownMenuGroup>
                    {menusList?.map(each =>
                        <Fragment key={each.id}>
                            {each?.route ?
                                <Link href={each?.route}>
                                    <DropdownMenuItem key={each.id} onClick={() => handleClickMenu(each.id)} className="cursor-pointer p-3">
                                        <div className="flex items-center gap-2 hover:text-secondary">
                                            <p className="h-4 w-4">{each?.labelIcon ? each?.labelIcon : each?.icon}</p>
                                            <p className="text-sm font-semibold ">{each?.label}</p>
                                        </div>
                                    </DropdownMenuItem>
                                </Link>
                                :
                                <DropdownMenuItem onClick={() => handleClickMenu(each.id)} className="cursor-pointer p-3">
                                    <div className="flex items-center gap-2 hover:text-secondary">
                                        <p className="h-4 w-4">{each?.labelIcon ? each?.labelIcon : each?.icon}</p>
                                        <p className="text-sm font-semibold ">{each?.label}</p>
                                    </div>
                                </DropdownMenuItem>}
                        </Fragment>
                    )}
                </DropdownMenuGroup>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}
export default DropdownMenusItem