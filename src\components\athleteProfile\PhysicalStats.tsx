"use client"
import { useHandleAthleteSectionExpose } from "@/hooks/useAthleteExposeSections"
import { useLocalStoredInfo } from "@/hooks/useLocalStoredInfo"
import { useTokenValues } from "@/hooks/useTokenValues"
import { AppDispatch, RootState } from "@/store"
import { fetchAthletePhysStats, handleUpdateUserInput, putAthletePhysStats } from "@/store/slices/athlete/athleteProfileSlice"
import { format } from "date-fns"
import { Loader } from "lucide-react"
import { ChangeEvent, useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { Button } from "../ui/button"
import { Input } from "../ui/input"
import { Label } from "../ui/label"
import { Skeleton } from "../ui/skeleton"
import { Switch } from "../ui/switch"

const PhysicalStats = () => {
    const { physicalStatsToggle, physStats, apiStatus, physStatsId } = useSelector((state: RootState) => state.athleteProfile)
    const [isEditTable, setIsEditable] = useState(false)
    const dispatch = useDispatch<AppDispatch>()
    const { handleToggleSections } = useHandleAthleteSectionExpose()
    const { roleId, userId } = useTokenValues()
    const { profileId } = useLocalStoredInfo()

    useEffect(() => {
        dispatch(fetchAthletePhysStats())
    }, [dispatch])

    const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
        const { name, value } = event.target
        dispatch(handleUpdateUserInput({ name: 'physStats', value: { ...physStats, [name]: value } }))
    }

    const handleEditPhyStats = () => {
        setIsEditable(!isEditTable)
        dispatch(handleUpdateUserInput({ name: 'physStats', value: null }))
    }


    const handleSavePhyStats = async () => {


        const payload = {
            roleId,
            athleteId: profileId,
            userId,
            heightInFeet: physStats?.hFeet,
            heightInches: physStats?.hInches,
            weightInLbs: physStats?.weight,
            lastUpdated: format(new Date(), 'yyyy-MM-dd'),
            isHidden: !physicalStatsToggle,
        }

        const physStatsAction = await dispatch(putAthletePhysStats(payload))
        if (putAthletePhysStats?.fulfilled?.match(physStatsAction)) {
            dispatch(handleUpdateUserInput({ name: 'physStats', value: null }))
            await dispatch(fetchAthletePhysStats())
        }
    }

    return (
        <>
            <div className="flex flex-col gap-4 bg-slate-100 p-4 rounded-lg">
                <div className="flex items-center justify-center gap-4">
                    <h3 className="font-bold text-xl text-center">Physical Stats</h3>
                    <Switch name='physicalStatsToggle' checked={physicalStatsToggle} onCheckedChange={(checked) => handleToggleSections("physicalStats", checked)} />
                </div>
                {apiStatus === 'fetchPhyStatPending' ?
                    <div className="flex items-center gap-6">
                        <Skeleton className="h-[20px] w-full rounded-lg bg-gray-300 animate-pulse" />
                        <Skeleton className="h-[20px] w-full rounded-lg bg-gray-300 animate-pulse" />
                        <Skeleton className="h-[20px] w-full rounded-lg bg-gray-300 animate-pulse" />
                    </div>
                    :
                    <>
                        {physicalStatsToggle ?
                            <>
                                {/* <div className="flex justify-end">
                            <Button
                                size={'icon'}
                                variant={'outline'}
                                onClick={handleEditPhyStats}
                            >
                                <PencilLine />
                            </Button>
                        </div> */}

                                {!isEditTable ?
                                    <div className="p-3 space-y-6">
                                        <div className="gap-10 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 justify-center items-end flex-wrap w-full">
                                            <div className="flex items-center gap-4 w-full">
                                                <img src={'/height.webp'} alt='Height' className="w-6" />
                                                <div className="flex flex-col gap-1  w-full">
                                                    <Label>Height in Feet</Label>
                                                    <Input
                                                        className="border-slate-300"
                                                        value={physStats?.hFeet}
                                                        name='hFeet'
                                                        onChange={handleChange}
                                                        placeholder="Height in Feet"
                                                    />
                                                </div>
                                            </div>

                                            <div className="flex flex-col gap-1 w-full">
                                                <Label>Height in Inches</Label>
                                                <Input
                                                    className="border-slate-300"
                                                    value={physStats?.hInches}
                                                    name='hInches'
                                                    onChange={handleChange}
                                                    placeholder="Height in Inches"
                                                />
                                            </div>

                                            <div className="flex items-center gap-3 w-full">
                                                <img src={'/weight.webp'} alt='Weight' className="w-6" />
                                                <div className="flex flex-col gap-1 w-full">
                                                    <Label>Weight in lbs</Label>
                                                    <Input
                                                        className="border-slate-300 w-full"
                                                        value={physStats?.weight}
                                                        name='weight'
                                                        onChange={handleChange}
                                                        placeholder="Weight in lbs"
                                                    />
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex justify-end w-full">
                                            <Button className="w-16 self-end"
                                                onClick={handleSavePhyStats}
                                                disabled={!physStats?.hFeet && !physStats?.weight}
                                            >
                                                {apiStatus === 'physStatsPending' || apiStatus === 'physStatsPutPending' ?
                                                    <Loader className="animate-spin text-white w-10 h-10" />
                                                    : 'Save'}
                                            </Button>
                                        </div>
                                    </div>
                                    :
                                    <div className="p-3 gap-12  flex justify-center items-end flex-wrap">
                                        <div className="flex items-center gap-6">
                                            <img src={'/height.webp'} alt='Height' className="w-6" />
                                            <p className="font-semibold">{physStats?.hFeet}</p>
                                            <p className="font-semibold">{physStats?.hInches}</p>
                                        </div>

                                        <div className="flex items-center gap-6">
                                            <img src={'/weight.webp'} alt='Weight' className="w-6" />
                                            <p className="font-semibold">{physStats?.weight}</p>
                                        </div>
                                    </div>
                                }
                            </>
                            :
                            null
                        }
                    </>}
            </div>
        </>
    )
}
export default PhysicalStats