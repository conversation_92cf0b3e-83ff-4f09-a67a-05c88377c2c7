"use client"
import { AppDispatch, RootState } from "@/store"
import { fetchAthleteBio, handleUpdateUserInput, putAthleteBio } from "@/store/slices/athlete/athleteProfileSlice"
import { Loader, PencilLine } from "lucide-react"
import { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"
import { Button } from "../ui/button"
import { Skeleton } from "../ui/skeleton"
import { Textarea } from "../ui/textarea"

const AboutAthlete = () => {
    const { apiStatus, isBioEditable, bio, profileCardData } = useSelector((state: RootState) => state.athleteProfile)
    const dispatch = useDispatch<AppDispatch>()

    const initialFetches = async () => {
        await dispatch(fetchAthleteBio())
    }

    useEffect(() => {
        initialFetches()
    }, [dispatch])

    const handleOnChange = (name, value) => {
        dispatch(handleUpdateUserInput({ name, value }))
    }

    const handleSave = async () => {
        const resutlAction = await dispatch(putAthleteBio({ bio }))
        if (putAthleteBio.fulfilled.match(resutlAction)) {
            await dispatch(fetchAthleteBio())
        }
    }

    return (
        <div className="flex flex-col justify-center gap-4">
            <div className="flex flex-col bg-slate-100 p-4 rounded-lg gap-3 overflow-hidden">
                <div className="flex items-center justify-center w-full gap-5">
                    <h3 className="font-bold text-center text-xl">About {(profileCardData?.firstName || "") + " " + (profileCardData?.lastName || '')}</h3>
                    {!isBioEditable && <div className="self-end">
                        <Button variant={'outline'} size={'icon'} onClick={() => handleOnChange('isBioEditable', !isBioEditable)}>
                            <PencilLine className="h-14 w-11" />
                        </Button>
                    </div>}
                </div>

                {apiStatus === 'fetchBioPending' ?
                    <div className="flex flex-col items-center gap-6">
                        <Skeleton className="h-[20px] w-full rounded-lg bg-slate-500" />
                        <Skeleton className="h-[20px] w-full rounded-lg bg-slate-500" />
                        <Skeleton className="h-[20px] w-full rounded-lg bg-slate-500" />
                    </div>
                    :
                    <>
                        {isBioEditable ?
                            <div>
                                <Textarea
                                    placeholder="Write here..."
                                    name='bio'
                                    value={bio}
                                    className="bg-white resize-none border-2 border-slate-300"
                                    rows={5}
                                    maxLength={400}
                                    onChange={(e) => handleOnChange('bio', e.target.value)}
                                />
                                <p className="text-destructive text-xs text-end">Max 400 chars</p>

                                <div className="flex justify-end gap-4 mt-4">
                                    <Button variant='outline' className="w-24" onClick={() => handleOnChange('isBioEditable', false)}>
                                        Cancel
                                    </Button>
                                    <Button className="w-24" onClick={handleSave} disabled={apiStatus === 'putBioPending'}>
                                        {apiStatus === 'putBioPending' ?
                                            <Loader className='h-4 w-4 animate-spin' />
                                            : 'Save'}
                                    </Button>
                                </div>
                            </div>
                            :
                            <>
                                {bio?.trim() ?
                                    <p className="font-semibold">
                                        {bio}
                                    </p> :
                                    <p className="text-center font-sm text-gray-500">
                                        You haven’t added Athlete bio yet. Share your story to let others know more about you!</p>
                                }
                            </>
                        }
                    </>}
            </div>
        </div>
    )
}
export default AboutAthlete