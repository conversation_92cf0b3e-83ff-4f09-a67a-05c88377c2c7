'use client'
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON><PERSON>er,
    <PERSON><PERSON><PERSON>eader,
    <PERSON><PERSON>Title,
    DialogTrigger
} from "@/components/ui/dialog";
import { AppDispatch } from "@/store";
import { deleteAthleteSportLinks, fetchAthleteSportLinks, postAthleteSportLinks, putAthleteSportLinks } from "@/store/slices/athlete/athleteSportProfileSlice";
import { Loader, PencilLine, Plus, Trash2 } from "lucide-react";
import { Params } from "next/dist/shared/lib/router/utils/route-matcher";
import { ChangeEvent, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Switch } from "../ui/switch";
import AlertPopup from "./AlertPopup";
import { useTokenValues } from "@/hooks/useTokenVal<PERSON>";
import { useLocalStoredInfo } from "@/hooks/useLocalStoredInfo";
import { ROLES } from "@/utils/constants";

interface IHighlightLink {
    id: number;
    text: string;
    url: string;
}

interface IProps {
    loading?: boolean;
    toggleHighlightLinks: boolean;
    highlightLinksList: IHighlightLink[];
    handleOnChange: (name: string, value: any) => void;
    params?: Params
    isEditHighlightLinks: boolean;
}

const emptyLink = { title: '', link: '' }

const SportHighLightLinks = ({
    loading,
    params,
    toggleHighlightLinks,
    highlightLinksList,
    handleOnChange,
    isEditHighlightLinks
}: IProps) => {
    const [localList, setLocalList] = useState(highlightLinksList);
    const [addModal, setAddModal] = useState<boolean>(false);
    const [addLink, setAddLink] = useState(emptyLink)
    const dispatch = useDispatch<AppDispatch>()
    const {profileId } = useLocalStoredInfo()
    const { userId, roleId, } = useTokenValues()
    const decodedSportName = decodeURIComponent(params?.sportName);

    useEffect(() => {
        if (highlightLinksList) {
            setLocalList(highlightLinksList);
        }
    }, [highlightLinksList]);

    const handleChange = (index: number, field: 'text' | 'url', value: string) => {
        const updated = [...localList]
        updated[index] = { ...updated[index], [field]: value };
        setLocalList(updated);
    };

    const handleOnChangeAdd = (event: ChangeEvent<HTMLInputElement>) => {
        const { name, value } = event.target
        setAddLink((prev) => ({ ...prev, [name]: value }))
    }

    const apiSuccessStatus = async (fetchAPI) => {
        setAddModal(false)
        setAddLink(emptyLink)
        await dispatch(fetchAPI(params?.sportId))
    }

    const handlePostAthleteLinks = async () => {
        const payload = {
            roleId,
            athleteId: profileId,
            userId,
            sportId: params?.sportId,
            highlightText: addLink?.title,
            highlightUrl: addLink?.link,
            isDeleted: false,
        }

        try {
            const resultAction = await dispatch(postAthleteSportLinks(payload))
            if (postAthleteSportLinks.fulfilled.match(resultAction)) {
                apiSuccessStatus(fetchAthleteSportLinks)
            }
        } catch (error) {
            console.log(error)
        }
    }

    const handlePutAthleteLinks = async () => {
        const payload = {
            highlights: localList?.map(each => ({
                roleId,
                athleteId: profileId,
                userId,
                sportId: Number(params?.sportId),
                highlightText: each?.text,
                highlightUrl: each?.url,
                isDeleted: false
            }))
        }

        try {
            const resultAction = await dispatch(putAthleteSportLinks(payload))
            if (putAthleteSportLinks.fulfilled.match(resultAction)) {
                apiSuccessStatus(fetchAthleteSportLinks)
                handleOnChange('isEditHighlightLinks', false)
            }
        } catch (error) {
            console.log(error)
        }
    }

    const handleSaveAddHighLightLink = async () => {
        if (roleId === ROLES.ATHLETE) {
            handlePostAthleteLinks()
        }
    }

    const handleEditSave = async () => {
        if (roleId === ROLES.ATHLETE) {
            handlePutAthleteLinks()
        }
    }

    const handleDelete = async (id) => {
        if (roleId === ROLES.ATHLETE) {
            try {
                const resultAction = await dispatch(deleteAthleteSportLinks(id))
                if (deleteAthleteSportLinks.fulfilled.match(resultAction)) {
                    await dispatch(fetchAthleteSportLinks(params?.sportId))
                }
            } catch (error) { }
        }
    }

    const handleClickAdd = () => {
        setAddModal(!addModal)
        setAddLink(emptyLink)
    }

    return (
        <div className="bg-slate-100 rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-center gap-5">
                <h3 className="text-xl font-bold">{decodedSportName} Highlight Links</h3>
                <Switch
                    checked={toggleHighlightLinks}
                    onCheckedChange={(checked) => handleOnChange("toggleHighlightLinks", checked)}
                />
            </div>

            {!isEditHighlightLinks && <div className="flex items-center gap-3 justify-end mb-4">
                <Dialog open={addModal} onOpenChange={handleClickAdd}>
                    <DialogTrigger asChild>
                        <div className="flex justify-end">
                            <Button variant={'outline'} className="self-end">
                                <Plus /> Add Link
                            </Button>
                        </div>
                    </DialogTrigger>
                    <DialogContent onInteractOutside={(event) => event.preventDefault()} className="sm:max-w-[425px]">
                        <DialogHeader>
                            <DialogTitle>Add HighLight Link</DialogTitle>
                        </DialogHeader>
                        <div className="flex flex-col gap-6 mt-5">
                            <div className="flex flex-col gap-1">
                                <Label>Title</Label>
                                <Input
                                    placeholder="Enter highlight title"
                                    value={addLink?.title}
                                    name='title'
                                    onChange={handleOnChangeAdd}
                                />
                            </div>
                            <div className="flex flex-col gap-1">
                                <Label>Link</Label>
                                <Input
                                    placeholder="Enter highlight link"
                                    value={addLink?.link}
                                    name='link'
                                    onChange={handleOnChangeAdd}
                                />
                            </div>
                        </div>
                        <DialogFooter>
                            <Button type="submit"
                                disabled={!addLink?.title || !addLink?.link}
                                onClick={handleSaveAddHighLightLink}
                            >
                                {loading ?
                                    <Loader className="animate-spin text-white w-10 h-10" />
                                    : 'Save'}
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>

                {toggleHighlightLinks && localList?.length > 0 && (<div className="flex items-center justify-end">
                    <Button
                        size="icon"
                        variant="outline"
                        onClick={() => handleOnChange("isEditHighlightLinks", !isEditHighlightLinks)}
                    >
                        <PencilLine />
                    </Button>
                </div>)}
            </div>}

            {toggleHighlightLinks && (
                <>
                    {localList?.length > 0 ? localList?.map((item, index) => (
                        <div key={item?.id + 'hll'} className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {isEditHighlightLinks ? (
                                <>
                                    <Input
                                        name='text'
                                        value={item?.text}
                                        placeholder="Link Text"
                                        onChange={(e) => handleChange(index, "text", e.target.value)}
                                    />
                                    <Input
                                        name='url'
                                        value={item?.url}
                                        placeholder="Link"
                                        onChange={(e) => handleChange(index, "url", e.target.value)}
                                    />
                                </>)
                                :
                                <>
                                    <div key={item?.id} className="w-full flex items-center gap-2">
                                        <div className='w-full flex items-center gap-1 justify-between bg-slate-200 rounded-md py-2'>
                                            <a
                                                href={item?.text}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="pl-3 w-36 sm:w-56 md:w-64 lg:w-72 text-blue-600 hover:underline overflow-hidden text-ellipsis whitespace-nowrap block"
                                                title={item?.text}
                                            >
                                                {item?.text}
                                            </a>
                                        </div>
                                        <div className='w-full flex items-center gap-1 justify-between bg-slate-200 rounded-md py-2'>
                                            <a
                                                href={item?.url}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="pl-3 w-36 sm:w-56 md:w-64 lg:w-72 text-blue-600 hover:underline overflow-hidden text-ellipsis whitespace-nowrap block"
                                                title={item?.url}
                                            >
                                                {item?.url}
                                            </a>
                                        </div>
                                        <div className="w-full">
                                            <AlertPopup
                                                trigger={<Button variant={'destructive'} size={'icon'}
                                                >
                                                    <Trash2 />
                                                </Button>
                                                }
                                                alertTitle="Confirm Deletion"
                                                alertContent="Are you sure, you want to delete?"
                                                action={() => handleDelete(item?.id)}
                                            />
                                        </div>
                                    </div>
                                </>
                            }
                        </div>))
                        :
                        <div className="flex items-center justify-center ">
                            <p className="text-center text-gray-600 text-sm py-5 w-3/4">
                                No highlight links found. Share your latest {decodedSportName} moments by uploading a new highlight links.
                            </p>
                        </div>
                    }

                    {isEditHighlightLinks && (
                        <div className="flex justify-end gap-3 pt-4">
                            <Button variant="outline" onClick={() => handleOnChange("isEditHighlightLinks", false)}>
                                Cancel
                            </Button>
                            <Button onClick={handleEditSave}>
                                {loading ?
                                    <Loader className="animate-spin text-white w-10 h-10" />
                                    : 'Save'}
                            </Button>
                        </div>
                    )}
                </>
            )}
        </div>
    );
};

export default SportHighLightLinks;