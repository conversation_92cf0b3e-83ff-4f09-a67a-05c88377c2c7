import axiosInstance from "@/utils/axiosInstance";
import { CoachProfileTypes } from "@/utils/interfaces";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

const initialState: CoachProfileTypes = {
  loading: false,
  error: "",
  profileToggle: false,
  coachpProfileData: null,
  isProfileEditable: false,
  selectedAgeGroups: [],
  selectedGender: [],
  toggleCoachingFocus: true,
  allCoachingFocusesList: [
    { value: 1, label: "General" },
    { value: 2, label: "Fitness" },
    { value: 3, label: "Strength" },
  ],
  selectedFocuses: [],
  toggleCoachingBackground: true,
  allCoachingBackground: [],
  selectedBackgrounds: [],
  toggleAboutVideo: true,
  aboutVideoFile: null,
  isBioEditable: false,
  toggleShortBio: true,
  shortBio: "",
  toggleSocialMedia: true,
  coachSocialMediaList: [
    {
      id: "x",
      icon: "/X.jpeg",
      link: "x.com",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "instagram",
      icon: "/instagram.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "facebook",
      icon: "/facebook.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "youtube",
      icon: "/youtube.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "linkedin",
      icon: "/linkedin.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
  ],
  toggleQuickLinks: true,
  coachQuickLinksList: [],
  toggleWebsite: true,
  website: "",
  toggleAffiliation: true,
  allAffiliationTypesList: [
    { value: 1, label: "General" },
    { value: 2, label: "School" },
  ],
  selectedAffiliationType: null,
  allCurrentAffiliations: [{ value: 1, label: "General" }],
  selectedCurrentAffiliation: null,
  currentAffiliation: "",
  allWhoAmIRightNowList: [
    { value: 1, label: "General" },
    { value: 2, label: "Other" },
  ],
  selectedWhoAmIRightNow: null,
  otherProfession: "",
  selectedIState: null,
  selectedILocations: [],
  toggleWhyIAm: true,
  allWhyIAmOptions: [
    { value: 1, label: "Give Back" },
    { value: 2, label: "Mentor Youth" },
    { value: 3, label: "Support Beyond Sports" },
  ],
  selectedWhyIAm: [],
  toggleMyCoachingFocus: true,
  allMyCoachingFocusList: [
    { value: 1, label: "Offering-private-training" },
    { value: 2, label: "Cpromoting-camps-clinics" },
    { value: 3, label: "Recruiting Athletes" },
    { value: 4, label: "Mentoring-youth" },
  ],
  selectedMyCoachingFocuses: [],
  toggleWhatIOfferAsCoach: true,
  allCoachOfferList: [
    { value: 1, label: "College-recruiting-preparation" },
    { value: 2, label: "Individual-skill-development" },
    { value: 3, label: "Tryout-preparation" },
    { value: 4, label: "Positive-mindset-coaching" },
  ],
  selectedCoachOffer: [],
  toggleSportInfoSection: true,
  coachSelectedSportsList: [

    {
        id: "1",
        isPrimary: true,
        sportName: "cricket",
        sportId: 1,
        sportLevel: "medim",
        specilities: [],
        isEditable: true
    }
  ],
  openVirtualSession: true,
  selectedState: null,
  selectedCounty: null,
  selectedLocations: [],
  coachAddedStateLocationsList: [],
  toggleHighLightVideo: true,
  highLightVideoData: null,
  toggleGallery: true,
  galleryData: null,
  galleriesList: [],
  toggleAvailability: true,
  allTimeZoneList: [
    { value: 1, label: "EST" },
    { value: 2, label: "IST" },
  ],
  availableTimeZone: null,
  generalAvailabilityList: [
    {
      id: "monday",
      day: "Monday",
      isAvailable: false,
      startTime: "",
      endTime: "",
    },
    {
      id: "tuesday",
      day: "Tuesday",
      isAvailable: false,
      startTime: "",
      endTime: "",
    },
    {
      id: "wednesday",
      day: "Wednesday",
      isAvailable: false,
      startTime: "",
      endTime: "",
    },
    {
      id: "thursday",
      day: "Thursday",
      isAvailable: false,
      startTime: "",
      endTime: "",
    },
    {
      id: "friday",
      day: "Friday",
      isAvailable: false,
      startTime: "",
      endTime: "",
    },
    {
      id: "saturday",
      day: "Saturday",
      isAvailable: false,
      startTime: "",
      endTime: "",
    },
    {
      id: "sunday",
      day: "Sunday",
      isAvailable: false,
      startTime: "",
      endTime: "",
    },
  ],
  toBookTime: undefined,
  toggleToBookTime: true,
  availabilityNote: "",
  toggleAvailabilityNote: true,
  toggleCertification: true,
  certificatesData: null,
  isAddCertificates: false,
  coachAddedCertificatesList: [],
  isEditCertificate: false,
  toggleResumeSection: true,
  coachResumeData: null,
  addedResumeData: null,
  toggleContactInfo: true,
  coachContactInfo: {
    firstName: "",
    lastName: "",
    phone: "",
    email: "",
    lastTAndCAccepted: "",
  },
  isEditContactInfo: false,
  togglePhone: true,
  toggleEmail: true,
  govtIdData: {
    title: "",
    documentType: undefined,
    otherType: "",
    expirationDate: undefined,
    file: null,
  },
  addedGovtIdData: null,
  additionalDocList: [
    {
      title: "",
      documentType: undefined,
      otherType: "",
      expirationDate: undefined,
      file: null,
    },
    {
      title: "",
      documentType: undefined,
      otherType: "",
      expirationDate: undefined,
      file: null,
    },
    {
      title: "",
      documentType: undefined,
      otherType: "",
      expirationDate: undefined,
      file: null,
    },
  ],
  declarations: {
    accuracy: false,
    responsibility: false,
    ongoing: false,
    consent: false,
    agreeAll: false,
    eSign: "",
    date: undefined,
  },
};


export const fetchCoachProfile = createAsyncThunk(
  "coachIntro/fetchCoachProfile",
  async (_, { fulfillWithValue, rejectWithValue }) => {

    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/coach-profile/${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
   

  }
);

const coachProfileSlice = createSlice({
  name: "coachProfile",
  initialState,
  reducers: {
    handleCoachInputChange: (state, action) => {
      const { name, value } = action.payload;
      state[name] = value;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchCoachProfile.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchCoachProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.coachpProfileData = action.payload.data;
      })
      .addCase(fetchCoachProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  }
});

export const { handleCoachInputChange } = coachProfileSlice.actions;
export default coachProfileSlice.reducer;
