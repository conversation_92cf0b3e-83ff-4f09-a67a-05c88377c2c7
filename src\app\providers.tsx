// app/providers.tsx
"use client";

import { AuthProvider } from "@/components/AuthProvider";
import { RouteLoader } from "@/components/RouteLoader";
import { SnackbarProvider } from "@/contexts/SnackbarContext";
import { ReduxProvider } from "@/store/provider";
import { SessionProvider } from "next-auth/react";
import { ReactNode } from "react";
import { ToastContainer } from "react-toastify";

export function Providers({ children }: { children: ReactNode }) {
    return (
        <SessionProvider>
            <ReduxProvider>
                <SnackbarProvider>
                    <RouteLoader />
                    <AuthProvider>
                        {children}
                    </AuthProvider>
                </SnackbarProvider>
            </ReduxProvider >
            <ToastContainer position="top-center" />
        </SessionProvider>
    )
}
