"use client";
import React from "react";

const AboutSection = () => {
  return (
    <section
      id="about"
      className="py-20 bg-[#0D1D3A] px-3 sm:px-8 md:px-16 lg:px-36 xl:px-56"
    >
      <h2 className="text-3xl text-center font-bold bg-gradient-to-r from-white via-blue-300 to-purple-400 bg-clip-text text-transparent leading-tight mb-6">
        A Founder’s Journey. A Platform for Every Aspiring Athlete.
      </h2>
      <div className="container mx-auto">
        <div className="flex flex-col md:flex-row gap-6 items-stretch">
          {/* Video Side */}
          <div className="flex-[1.3]">
            <video
              className="w-full h-full rounded-lg shadow-lg object-cover"
              autoPlay
              muted
              loop
              playsInline
            >
              <source
                src="/landing-page-videos/connect-athlete-promotional-video-2.mp4"
                type="video/mp4"
              />
              Your browser does not support the video tag.
            </video>
          </div>

          {/* Text Side */}
          <div className="flex-[1] space-y-6 flex flex-col justify-center text-gray-200">
            <div className="cms-text space-y-4">
              <p>
                Connect Athlete was born from our founder’s own story—a mom
                trying to help her talented son chase his dream in sports, only
                to face a maze of uncertainty, missed connections, and expensive
                guesswork. She quickly realized: it’s not a lack of talent
                holding athletes back—it’s the lack of access, exposure, and
                trusted guidance. Too many families are left to figure it out
                alone.
              </p>
              <p>
                That’s why she built Connect Athlete—a unified platform that
                brings together the entire youth sports ecosystem. Powered by
                smart tools and our proprietary SPARQX™ engine, it supports
                every step of an athlete’s development journey. From growth to
                mentorship, visibility to wellness—we’re here to open doors that
                once felt closed.
              </p>
              <p className="font-bold">
                Because every athlete has potential. What they need is a path.
              </p>
            </div>
          </div>
        </div>

        {/* Quote Section */}
        <div className="mt-12 text-center max-w-4xl mx-auto">
          <blockquote className="text-gray-100 italic text-lg sm:text-xl leading-relaxed border-l-4 border-blue-500 pl-4 text-left">
            “When we come together as a community for our youth—for our
            students—it transcends belief, background, or culture. It becomes
            about lifting each other up, knowing that life changes and we all
            need support sometimes. My mission is to use innovation to build
            that kind of community—one rooted in strength, empathy, and
            collective growth. Because we grow every day—but we grow stronger
            together for a future that will outlive us all.”
          </blockquote>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
