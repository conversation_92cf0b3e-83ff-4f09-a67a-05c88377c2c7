import { EachSearchItem } from "@/utils/interfaces";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axios from "axios";

interface OrgProfileState {
  loading: boolean;
  error: string | null;
  orgProfile: any[];
  allStates: any[];
  quickLinksToggle: boolean;
  orgQuickLinksList: { id: string; link: string; isEditable: boolean }[];
  orgSocialMediaList: {
    id: string;
    icon: string;
    link: string;
    isEditable: boolean;
    isEnable: boolean;
  }[];
  selectedState: EachSearchItem | null;
  selectedLocations: EachSearchItem[];
  selectedSport: EachSearchItem | null;
  selectedSpecialities: EachSearchItem[];
}

const initialState: OrgProfileState = {
  loading: false,
  error: null,
  orgProfile: [],
  allStates: [],
  quickLinksToggle: true,
  orgQuickLinksList: [
    { id: "quickLink1", link: "", isEditable: false },
    { id: "quickLink2", link: "", isEditable: false },
    { id: "quickLink3", link: "", isEditable: false },
  ],
  orgSocialMediaList: [
    {
      id: "x",
      icon: "/X.jpeg",
      link: "x.com",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "instagram",
      icon: "/instagram.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "facebook",
      icon: "/facebook.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "youtube",
      icon: "/youtube.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
  ],
  selectedState: null,
  selectedLocations: [],
  selectedSport: null,
  selectedSpecialities: [],
};

export const fetchOrgProfile = createAsyncThunk(
  "org/Profile",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const token = JSON.parse(localStorage.getItem("token") || '""');
      const profileId = localStorage.getItem("profileId");

      const url = `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/allOrganization?id=${profileId}`;
      if (!url) {
        throw new Error("User profile API URL is not defined.");
      }

      const response = await axios.get(url, {
        headers: {
          Authorization: token,
          "Content-Type": "application/json",
        },
      });

      if (response?.data?.status === 200) {
        return fulfillWithValue(response.data.data);
      } else {
        return rejectWithValue(
          response?.data?.message || "Failed to fetch org profile"
        );
      }
    } catch (error) {
      if (error instanceof Error) {
        console.error("fetchOrgProfile error:", error.message);
        return rejectWithValue(error.message);
      }
      console.error("fetchOrgProfile unknown error:", error);
      return rejectWithValue("An unknown error occurred");
    }
  }
);

export const fetchAllStates = createAsyncThunk(
  "org/getAllStates",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const rawToken = localStorage.getItem("token");
      if (!rawToken) return rejectWithValue("Authorization token missing");

      const url = process.env.NEXT_PUBLIC_USER_MANAGEMENT_GET_ALL_STATES;
      if (!url) throw new Error("All States API URL is not defined.");

      console.log("Fetching all states from:", url);

      const response = await axios.get(url);

      if (response.data?.status === 200) {
        return fulfillWithValue(response.data.data);
      } else {
        return rejectWithValue(
          response.data?.message || "Failed to fetch all states"
        );
      }
    } catch (error: any) {
      if (error.response) {
        console.error("API error response:", error.response.data);
        return rejectWithValue(
          error.response.data.message || "API error occurred"
        );
      } else if (error.request) {
        console.error("No response received:", error.request);
        return rejectWithValue("No response from API");
      } else {
        console.error("Error:", error.message);
        return rejectWithValue(error.message || "Unknown error");
      }
    }
  }
);

export const OrgProfileSlice = createSlice({
  name: "orgProfile",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // fetchOrgProfile cases
      .addCase(fetchOrgProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchOrgProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.orgProfile = action.payload;
      })
      .addCase(fetchOrgProfile.rejected, (state, action) => {
        state.loading = false;
        state.error =
          (action.payload as string) || "Failed to fetch org profile";
      })

      // fetchAllStates cases
      .addCase(fetchAllStates.pending, (state) => {
        console.log("fetchAllStates payload in pending:");
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllStates.fulfilled, (state, action) => {
        console.log("fetchAllStates payload in reducer:", action.payload);
        state.loading = false;
        state.allStates = action.payload;
      })
      .addCase(fetchAllStates.rejected, (state, action) => {
        console.log("fetchAllStates payload in rejected:");
        state.loading = false;
        state.error =
          (action.payload as string) || "Failed to fetch all states";
      });
  },
});

export default OrgProfileSlice.reducer;
