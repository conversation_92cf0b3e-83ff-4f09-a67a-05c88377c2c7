'use client'

import { Label } from "@/components/ui/label";


interface IProps {
    linkTrackList: { id: string, img: string, label: string, }[]
}
const SectionsLinkTracker = ({ linkTrackList }: IProps) => {

    const handleScroll = (id: string) => {
        const element = document.getElementById(id);
        if (element) {
            const yOffset = -80;
            const y = element.getBoundingClientRect().top + window.pageYOffset + yOffset;
            window.scrollTo({ top: y, behavior: 'smooth' });
        }
    }

    return (
        <div className="bg-slate-100 p-4 rounded-xl grid grid-cols-3 lg:grid-cols-4 gap-8">
            {linkTrackList?.map(item =>
                <div
                    key={item?.id}
                    onClick={() => handleScroll(item.id)}
                    className="flex flex-col gap-1 items-center justify-center cursor-pointer"
                >
                    <img src={item.img} className="w-8 h-8 object-contain" alt={item.label} />
                    <Label className="text-[15px] font-semibold text-center">{item.label}</Label>
                </div>
            )}
        </div>
    );
};

export default SectionsLinkTracker;
